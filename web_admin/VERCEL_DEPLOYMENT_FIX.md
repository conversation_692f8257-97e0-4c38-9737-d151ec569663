# Vercel Deployment Fix Guide

## Problem
The `npm run build` command is failing during Vercel deployment with exit code 1, likely due to missing Firebase environment variables.

## Root Cause
The build process requires Firebase configuration environment variables that are not set in Vercel's deployment environment.

## Solution

### Step 1: Get Firebase Configuration Values
1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `mcq-quiz-system`
3. Go to **Project Settings** → **General** → **Your apps**
4. Find your web app and copy the config object values

### Step 2: Add Environment Variables to Vercel

#### Option A: Via Vercel Dashboard (Recommended)
1. Go to your [Vercel Dashboard](https://vercel.com/dashboard)
2. Select your project
3. Go to **Settings** → **Environment Variables**
4. Add the following variables:

| Variable Name | Value | Environment |
|---------------|-------|-------------|
| `REACT_APP_FIREBASE_API_KEY` | Your Firebase API key | Production, Preview, Development |
| `REACT_APP_FIREBASE_AUTH_DOMAIN` | your-project-id.firebaseapp.com | Production, Preview, Development |
| `REACT_APP_FIREBASE_PROJECT_ID` | your-project-id | Production, Preview, Development |
| `REACT_APP_FIREBASE_STORAGE_BUCKET` | your-project-id.appspot.com | Production, Preview, Development |
| `REACT_APP_FIREBASE_MESSAGING_SENDER_ID` | Your messaging sender ID | Production, Preview, Development |
| `REACT_APP_FIREBASE_APP_ID` | Your Firebase app ID | Production, Preview, Development |
| `REACT_APP_FIREBASE_MEASUREMENT_ID` | Your measurement ID (optional) | Production, Preview, Development |

#### Option B: Via Vercel CLI
```bash
# Install Vercel CLI if not already installed
npm i -g vercel

# Set environment variables
vercel env add REACT_APP_FIREBASE_API_KEY
vercel env add REACT_APP_FIREBASE_AUTH_DOMAIN
vercel env add REACT_APP_FIREBASE_PROJECT_ID
vercel env add REACT_APP_FIREBASE_STORAGE_BUCKET
vercel env add REACT_APP_FIREBASE_MESSAGING_SENDER_ID
vercel env add REACT_APP_FIREBASE_APP_ID
vercel env add REACT_APP_FIREBASE_MEASUREMENT_ID
```

### Step 3: Redeploy
After adding the environment variables:
1. Go to your Vercel project dashboard
2. Go to **Deployments** tab
3. Click **Redeploy** on the latest deployment
4. Or push a new commit to trigger automatic deployment

### Step 4: Verify Build Success
The build should now complete successfully. You can verify by:
1. Checking the deployment logs in Vercel
2. Visiting your deployed application
3. Checking browser console for any Firebase configuration errors

## Additional Notes

### Local Development
For local development, create a `.env.local` file in the `web_admin` directory:
```bash
# Copy from .env.example and fill in your values
cp .env.example .env.local
```

### Security
- Never commit actual Firebase keys to your repository
- Use Vercel's environment variables for production
- The `.env.example` file shows the required format

### Troubleshooting
If the build still fails:
1. Check Vercel deployment logs for specific error messages
2. Verify all environment variables are set correctly
3. Ensure Firebase project is properly configured
4. Check that the Firebase project ID matches your actual project

### Build Command Override (if needed)
If you need to override the build command in Vercel:
1. Go to **Settings** → **General** → **Build & Development Settings**
2. Set **Build Command** to: `npm run build`
3. Set **Output Directory** to: `build`

## Files Created
- `vercel.json` - Vercel configuration file
- This guide - `VERCEL_DEPLOYMENT_FIX.md`

The `vercel.json` file configures Vercel to properly build and serve your React application with the correct routing for a single-page application.
