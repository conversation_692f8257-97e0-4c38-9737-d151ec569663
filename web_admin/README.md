# MCQ Quiz System - Web Admin Panel

A comprehensive web-based admin panel for managing the MCQ Quiz System, built with React, TypeScript, and Material-UI.

## 🚀 Features

### 📊 Dashboard
- **Analytics Overview** - Real-time statistics and performance metrics
- **User Management** - Monitor mobile app users and their activities
- **Quick Actions** - Easy access to frequently used admin functions

### 📝 Question Management
- **Question Creation** - Add new questions with multiple choice options
- **Category Management** - Organize questions by subjects and topics
- **Bulk Upload** - Import questions from Excel/CSV files
- **Question Editing** - Modify existing questions and answers

### 🎯 Exam Management
- **Exam Creation** - Create custom exams with specific parameters
- **Live Test Management** - Schedule and manage timed exams
- **Difficulty Levels** - Set easy, medium, and hard difficulty levels
- **Exam Suitability** - Target specific user groups (MTS, Postman, PA, etc.)

### 👥 User Management
- **Mobile User Analytics** - View user statistics and performance
- **User Activity Monitoring** - Track user engagement and progress
- **Performance Reports** - Generate detailed user performance reports

### 🎨 Content Management
- **Banner Management** - Update promotional banners for mobile app
- **Featured Quizzes** - Manage highlighted quiz content
- **App Settings** - Configure mobile app settings and preferences

### 📈 Analytics & Reports
- **Performance Metrics** - Detailed analytics on user performance
- **Export Functionality** - Download reports in PDF/Excel format
- **Real-time Data** - Live updates from Firebase backend

## 🛠️ Technology Stack

- **Frontend**: React 18 with TypeScript
- **UI Framework**: Material-UI (MUI) v5
- **State Management**: React Query + Context API
- **Routing**: React Router v6
- **Forms**: React Hook Form with Yup validation
- **Charts**: Recharts & MUI X Charts
- **Backend**: Firebase (Firestore, Authentication)
- **File Processing**: XLSX for Excel import/export
- **PDF Generation**: jsPDF for report generation

## 📦 Installation

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Firebase project setup

### Setup Instructions

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd web_admin
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Firebase Configuration**
   - Create a Firebase project at [Firebase Console](https://console.firebase.google.com)
   - Enable Firestore Database and Authentication
   - Copy your Firebase config and update `src/config/firebase.ts`

4. **Environment Variables**
   Create a `.env` file in the root directory:
   ```env
   REACT_APP_FIREBASE_API_KEY=your_api_key
   REACT_APP_FIREBASE_AUTH_DOMAIN=your_auth_domain
   REACT_APP_FIREBASE_PROJECT_ID=your_project_id
   REACT_APP_FIREBASE_STORAGE_BUCKET=your_storage_bucket
   REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   REACT_APP_FIREBASE_APP_ID=your_app_id
   ```

5. **Start the development server**
   ```bash
   npm start
   # or
   yarn start
   ```

6. **Build for production**
   ```bash
   npm run build
   # or
   yarn build
   ```

## 🔧 Available Scripts

- `npm start` - Start development server
- `npm run build` - Build for production
- `npm test` - Run tests
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Fix ESLint issues
- `npm run format` - Format code with Prettier

## 📁 Project Structure

```
web_admin/
├── public/                 # Static files
├── src/
│   ├── components/        # Reusable UI components
│   ├── pages/            # Page components
│   ├── services/         # API services
│   ├── contexts/         # React contexts
│   ├── utils/           # Utility functions
│   ├── config/          # Configuration files
│   └── App.tsx          # Main app component
├── package.json
└── README.md
```

## 🔐 Authentication

The admin panel uses Firebase Authentication with email/password login. Admin users must be manually added to the system.

## 🚀 Deployment

### Firebase Hosting
1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login: `firebase login`
3. Initialize: `firebase init hosting`
4. Build: `npm run build`
5. Deploy: `firebase deploy`

### Other Hosting Platforms
The built files in the `build/` directory can be deployed to any static hosting service like Netlify, Vercel, or AWS S3.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions, please contact the development team or create an issue in the repository.

## 🔄 Version History

- **v1.0.0** - Initial release with core admin functionality
- Features: Dashboard, Question Management, User Analytics, Exam Creation
