# Firebase Configuration
# Get these values from your Firebase project settings
REACT_APP_FIREBASE_API_KEY=your_firebase_api_key_here
REACT_APP_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your_project_id_here
REACT_APP_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id_here
REACT_APP_FIREBASE_APP_ID=your_firebase_app_id_here

# Optional: Firebase Measurement ID for Analytics
REACT_APP_FIREBASE_MEASUREMENT_ID=your_measurement_id_here

# Application Configuration
REACT_APP_APP_NAME=MCQ Quiz Admin Panel
REACT_APP_VERSION=1.0.0

# API Configuration (if using external APIs)
REACT_APP_API_BASE_URL=https://your-api-domain.com/api

# Development Configuration
REACT_APP_DEBUG_MODE=false
REACT_APP_LOG_LEVEL=info

# Feature Flags
REACT_APP_ENABLE_ANALYTICS=true
REACT_APP_ENABLE_NOTIFICATIONS=true
REACT_APP_ENABLE_BULK_UPLOAD=true

# Security Configuration
REACT_APP_ADMIN_EMAIL_DOMAIN=@yourdomain.com
