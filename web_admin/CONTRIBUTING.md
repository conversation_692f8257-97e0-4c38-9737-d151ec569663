# Contributing to MCQ Quiz Admin Panel

Thank you for your interest in contributing to the MCQ Quiz Admin Panel! This document provides guidelines and information for contributors.

## 🤝 How to Contribute

### Reporting Issues
- Use the GitHub issue tracker to report bugs
- Include detailed information about the issue
- Provide steps to reproduce the problem
- Include screenshots if applicable

### Suggesting Features
- Open an issue with the "feature request" label
- Describe the feature and its benefits
- Explain the use case and expected behavior

### Code Contributions

1. **Fork the Repository**
   ```bash
   git clone https://github.com/yourusername/mcq-quiz-admin.git
   cd mcq-quiz-admin/web_admin
   ```

2. **Create a Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

3. **Make Your Changes**
   - Follow the coding standards
   - Write tests for new functionality
   - Update documentation as needed

4. **Test Your Changes**
   ```bash
   npm test
   npm run lint
   npm run build
   ```

5. **Commit Your Changes**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

6. **Push and Create Pull Request**
   ```bash
   git push origin feature/your-feature-name
   ```

## 📝 Coding Standards

### TypeScript Guidelines
- Use TypeScript for all new code
- Define proper interfaces and types
- Avoid using `any` type
- Use meaningful variable and function names

### React Best Practices
- Use functional components with hooks
- Implement proper error boundaries
- Use React.memo for performance optimization
- Follow the single responsibility principle

### Code Style
- Use Prettier for code formatting
- Follow ESLint rules
- Use meaningful commit messages
- Write self-documenting code

### File Organization
```
src/
├── components/          # Reusable UI components
│   ├── common/         # Generic components
│   └── specific/       # Feature-specific components
├── pages/              # Page components
├── services/           # API and external services
├── hooks/              # Custom React hooks
├── utils/              # Utility functions
├── types/              # TypeScript type definitions
└── constants/          # Application constants
```

## 🧪 Testing Guidelines

### Unit Tests
- Write tests for all utility functions
- Test React components with React Testing Library
- Aim for high test coverage
- Mock external dependencies

### Integration Tests
- Test component interactions
- Test API integrations
- Test user workflows

### Running Tests
```bash
# Run all tests
npm test

# Run tests with coverage
npm test -- --coverage

# Run tests in watch mode
npm test -- --watch
```

## 🚀 Development Setup

### Prerequisites
- Node.js 16+
- npm or yarn
- Firebase project

### Environment Setup
1. Copy `.env.example` to `.env`
2. Fill in your Firebase configuration
3. Install dependencies: `npm install`
4. Start development server: `npm start`

### Firebase Setup
1. Create a Firebase project
2. Enable Firestore and Authentication
3. Set up security rules
4. Configure hosting (optional)

## 📋 Pull Request Guidelines

### Before Submitting
- [ ] Code follows the style guidelines
- [ ] Tests pass locally
- [ ] New tests added for new functionality
- [ ] Documentation updated
- [ ] No console.log statements left in code
- [ ] TypeScript types are properly defined

### PR Description Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests added/updated
- [ ] Manual testing completed

## Screenshots (if applicable)
Add screenshots of UI changes

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Tests pass
- [ ] Documentation updated
```

## 🐛 Bug Reports

### Bug Report Template
```markdown
**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
What you expected to happen.

**Screenshots**
If applicable, add screenshots.

**Environment:**
- OS: [e.g. iOS]
- Browser [e.g. chrome, safari]
- Version [e.g. 22]

**Additional context**
Any other context about the problem.
```

## 📚 Resources

- [React Documentation](https://reactjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs)
- [Material-UI Documentation](https://mui.com)
- [Firebase Documentation](https://firebase.google.com/docs)

## 📞 Getting Help

- Create an issue for bugs or feature requests
- Join our development discussions
- Contact the maintainers for urgent issues

Thank you for contributing! 🎉
