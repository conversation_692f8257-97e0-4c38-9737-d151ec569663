<lint-module
    format="1"
    dir="/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.1.0/android"
    name=":permission_handler_android"
    type="LIBRARY"
    maven="com.baseflow.permissionhandler:permission_handler_android:"
    agpVersion="8.3.0"
    buildFolder="/Volumes/sathish/mcq/mobile_app/build/permission_handler_android"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-34/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true"/>
  <variant name="release"/>
</lint-module>
