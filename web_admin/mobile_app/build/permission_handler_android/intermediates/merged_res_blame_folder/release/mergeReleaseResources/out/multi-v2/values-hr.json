{"logs": [{"outputFile": "com.baseflow.permissionhandler.permission_handler_android-release-25:/values-hr/values-hr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}}]}, {"outputFile": "com.baseflow.permissionhandler.permission_handler_android-mergeReleaseResources-23:/values-hr/values-hr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-hr/values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}}]}]}