<libraries>
  <library
      name="io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/io.flutter/flutter_embedding_release/1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7/8d57ee6078955428315d9cd81432c21f112c3bd2/flutter_embedding_release-1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7.jar"
      resolved="io.flutter:flutter_embedding_release:1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7"/>
  <library
      name="androidx.fragment:fragment:1.7.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/4860d23bece28dfe879b5deeaa710c16/transformed/fragment-1.7.1/jars/classes.jar"
      resolved="androidx.fragment:fragment:1.7.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/4860d23bece28dfe879b5deeaa710c16/transformed/fragment-1.7.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.activity:activity:1.8.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/d5d12c35985b20a558d4e973aba5e297/transformed/jetified-activity-1.8.1/jars/classes.jar"
      resolved="androidx.activity:activity:1.8.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/d5d12c35985b20a558d4e973aba5e297/transformed/jetified-activity-1.8.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.loader:loader:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/e142e24b615071a5dd7668dbcb0025ee/transformed/loader-1.0.0/jars/classes.jar"
      resolved="androidx.loader:loader:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/e142e24b615071a5dd7668dbcb0025ee/transformed/loader-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/c90390a0dd8d9ad2dc30acba256a3c3f/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/c90390a0dd8d9ad2dc30acba256a3c3f/transformed/jetified-lifecycle-livedata-core-ktx-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/c486e1e84f2fe4a152776f4eede56cf6/transformed/lifecycle-livedata-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/c486e1e84f2fe4a152776f4eede56cf6/transformed/lifecycle-livedata-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/c7ef2a33cfb9890d47a7fd7de4b6fe34/transformed/lifecycle-viewmodel-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/c7ef2a33cfb9890d47a7fd7de4b6fe34/transformed/lifecycle-viewmodel-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-livedata-core:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/feb68f2293745d18d44deace931e2b8d/transformed/lifecycle-livedata-core-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-livedata-core:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/feb68f2293745d18d44deace931e2b8d/transformed/lifecycle-livedata-core-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/e607d3f2bc2cd8de7126b0c50e0eb2ae/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/e607d3f2bc2cd8de7126b0c50e0eb2ae/transformed/jetified-lifecycle-viewmodel-savedstate-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core-ktx:1.13.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/40bacb72aea694fa3478281aa21872e7/transformed/jetified-core-ktx-1.13.1/jars/classes.jar"
      resolved="androidx.core:core-ktx:1.13.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/40bacb72aea694fa3478281aa21872e7/transformed/jetified-core-ktx-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.viewpager:viewpager:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/496f60f0ef1c5f441bab0b8f9270bc93/transformed/viewpager-1.0.0/jars/classes.jar"
      resolved="androidx.viewpager:viewpager:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/496f60f0ef1c5f441bab0b8f9270bc93/transformed/viewpager-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.customview:customview:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/5b80d268a08fbe415ebf0442ec112170/transformed/customview-1.0.0/jars/classes.jar"
      resolved="androidx.customview:customview:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/5b80d268a08fbe415ebf0442ec112170/transformed/customview-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.core:core:1.13.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/jars/classes.jar"
      resolved="androidx.core:core:1.13.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-runtime:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/cf775d1018e8242f9dd279f790c41ff6/transformed/lifecycle-runtime-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-runtime:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/cf775d1018e8242f9dd279f790c41ff6/transformed/lifecycle-runtime-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-process:2.7.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/0079ab4e1f06d8c622354be9133b477c/transformed/jetified-lifecycle-process-2.7.0/jars/classes.jar"
      resolved="androidx.lifecycle:lifecycle-process:2.7.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/0079ab4e1f06d8c622354be9133b477c/transformed/jetified-lifecycle-process-2.7.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.lifecycle:lifecycle-common-java8:2.7.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common-java8/2.7.0/2ad14aed781c4a73ed4dbb421966d408a0a06686/lifecycle-common-java8-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common-java8:2.7.0"/>
  <library
      name="androidx.lifecycle:lifecycle-common:2.7.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.lifecycle/lifecycle-common/2.7.0/85334205d65cca70ed0109c3acbd29e22a2d9cb1/lifecycle-common-2.7.0.jar"
      resolved="androidx.lifecycle:lifecycle-common:2.7.0"/>
  <library
      name="androidx.arch.core:core-runtime:2.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/eaec9786b07b560e352d1096aadac043/transformed/core-runtime-2.2.0/jars/classes.jar"
      resolved="androidx.arch.core:core-runtime:2.2.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/eaec9786b07b560e352d1096aadac043/transformed/core-runtime-2.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.arch.core:core-common:2.2.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.arch.core/core-common/2.2.0/5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3/core-common-2.2.0.jar"
      resolved="androidx.arch.core:core-common:2.2.0"/>
  <library
      name="androidx.versionedparcelable:versionedparcelable:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/ee93932d41e048238228b58d39b07717/transformed/versionedparcelable-1.1.1/jars/classes.jar"
      resolved="androidx.versionedparcelable:versionedparcelable:1.1.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/ee93932d41e048238228b58d39b07717/transformed/versionedparcelable-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.collection:collection:1.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.collection/collection/1.1.0/1f27220b47669781457de0d600849a5de0e89909/collection-1.1.0.jar"
      resolved="androidx.collection:collection:1.1.0"/>
  <library
      name="androidx.savedstate:savedstate:1.2.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/72c171969d9aa1207ea564e61d0060ec/transformed/jetified-savedstate-1.2.1/jars/classes.jar"
      resolved="androidx.savedstate:savedstate:1.2.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/72c171969d9aa1207ea564e61d0060ec/transformed/jetified-savedstate-1.2.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-jvm:1.9.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.annotation/annotation-jvm/1.9.1/b17951747e38bf3986a24431b9ba0d039958aa5f/annotation-jvm-1.9.1.jar"
      resolved="androidx.annotation:annotation-jvm:1.9.1"/>
  <library
      name="androidx.window:window:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/61f0876d449e18dd591fe0719c09f0b8/transformed/jetified-window-1.2.0/jars/classes.jar"
      resolved="androidx.window:window:1.2.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/61f0876d449e18dd591fe0719c09f0b8/transformed/jetified-window-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.window:window-java:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/9299fbf4739b7f061987804992df2b17/transformed/jetified-window-java-1.2.0/jars/classes.jar"
      resolved="androidx.window:window-java:1.2.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/9299fbf4739b7f061987804992df2b17/transformed/jetified-window-java-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.annotation:annotation-experimental:1.4.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/20fcb5dd09a31d1e8cf22280ffb3bbfb/transformed/jetified-annotation-experimental-1.4.0/jars/classes.jar"
      resolved="androidx.annotation:annotation-experimental:1.4.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/20fcb5dd09a31d1e8cf22280ffb3bbfb/transformed/jetified-annotation-experimental-1.4.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-android/1.7.1/c2d86b569f10b7fc7e28d3f50c0eed97897d77a7/kotlinx-coroutines-android-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.1"/>
  <library
      name="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlinx/kotlinx-coroutines-core-jvm/1.7.1/63a0779cf668e2a47d13fda7c3b0c4f8dc7762f4/kotlinx-coroutines-core-jvm-1.7.1.jar"
      resolved="org.jetbrains.kotlinx:kotlinx-coroutines-core-jvm:1.7.1"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk8/1.8.20/73576ddf378c5b4f1f6b449fe6b119b8183fc078/kotlin-stdlib-jdk8-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk8:1.8.20"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib-jdk7/1.8.20/3aa51faf20aae8b31e1a4bc54f8370673d7b7df4/kotlin-stdlib-jdk7-1.8.20.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.8.20"/>
  <library
      name="org.jetbrains.kotlin:kotlin-stdlib:1.9.24@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains.kotlin/kotlin-stdlib/1.9.24/9928532f12c66ad816a625b3f9984f8368ca6d2b/kotlin-stdlib-1.9.24.jar"
      resolved="org.jetbrains.kotlin:kotlin-stdlib:1.9.24"/>
  <library
      name="org.jetbrains:annotations:23.0.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/org.jetbrains/annotations/23.0.0/8cc20c07506ec18e0834947b84a864bfc094484e/annotations-23.0.0.jar"
      resolved="org.jetbrains:annotations:23.0.0"/>
  <library
      name="androidx.startup:startup-runtime:1.1.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/36abb3ee88f96ca55d6f748ff118f148/transformed/jetified-startup-runtime-1.1.1/jars/classes.jar"
      resolved="androidx.startup:startup-runtime:1.1.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/36abb3ee88f96ca55d6f748ff118f148/transformed/jetified-startup-runtime-1.1.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.tracing:tracing:1.2.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/c3534323b398b810a38d16dcdd48d5dc/transformed/jetified-tracing-1.2.0/jars/classes.jar"
      resolved="androidx.tracing:tracing:1.2.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/c3534323b398b810a38d16dcdd48d5dc/transformed/jetified-tracing-1.2.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.getkeepsafe.relinker:relinker:1.4.5@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/5bc13ad6d50769dbafcea428fd8aff5f/transformed/jetified-relinker-1.4.5/jars/classes.jar"
      resolved="com.getkeepsafe.relinker:relinker:1.4.5"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/5bc13ad6d50769dbafcea428fd8aff5f/transformed/jetified-relinker-1.4.5"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.profileinstaller:profileinstaller:1.3.1@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/26032761b3b6f8a5ee871fe43878859a/transformed/jetified-profileinstaller-1.3.1/jars/classes.jar"
      resolved="androidx.profileinstaller:profileinstaller:1.3.1"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/26032761b3b6f8a5ee871fe43878859a/transformed/jetified-profileinstaller-1.3.1"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.interpolator:interpolator:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/15bbc6caf50629988178dfb3c39a85fe/transformed/interpolator-1.0.0/jars/classes.jar"
      resolved="androidx.interpolator:interpolator:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/15bbc6caf50629988178dfb3c39a85fe/transformed/interpolator-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="androidx.concurrent:concurrent-futures:1.1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/androidx.concurrent/concurrent-futures/1.1.0/50b7fb98350d5f42a4e49704b03278542293ba48/concurrent-futures-1.1.0.jar"
      resolved="androidx.concurrent:concurrent-futures:1.1.0"/>
  <library
      name="androidx.window.extensions.core:core:1.0.0@aar"
      jars="/Users/<USER>/.gradle/caches/8.12/transforms/5ddb25aef2751409dfe825a1a40983d6/transformed/jetified-core-1.0.0/jars/classes.jar"
      resolved="androidx.window.extensions.core:core:1.0.0"
      folder="/Users/<USER>/.gradle/caches/8.12/transforms/5ddb25aef2751409dfe825a1a40983d6/transformed/jetified-core-1.0.0"
      manifest="AndroidManifest.xml"
      resFolder="res"
      assetsFolder="assets"
      lintJar="lint.jar"
      publicResources="public.txt"
      symbolFile="R.txt"
      externalAnnotations="annotations.zip"
      proguardRules="proguard.txt"/>
  <library
      name="com.google.guava:listenablefuture:1.0@jar"
      jars="/Users/<USER>/.gradle/caches/modules-2/files-2.1/com.google.guava/listenablefuture/1.0/c949a840a6acbc5268d088e47b04177bf90b3cad/listenablefuture-1.0.jar"
      resolved="com.google.guava:listenablefuture:1.0"/>
</libraries>
