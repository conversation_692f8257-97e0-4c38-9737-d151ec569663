<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2018 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
              android:layout_width="match_parent"
              android:layout_height="match_parent"
              android:orientation="vertical">

    <LinearLayout android:layout_width="match_parent"
                  android:layout_height="wrap_content"
                  android:orientation="vertical">

        <TextView
            android:id="@+id/fingerprint_subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="24dp"
            android:layout_marginStart="24dp"
            android:textColor="?android:attr/textColorSecondary"
            android:textSize="16sp"
            android:singleLine="true"
            android:ellipsize="marquee"
            android:marqueeRepeatLimit="marquee_forever"/>

        <TextView
            android:id="@+id/fingerprint_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="0dp"
            android:layout_marginEnd="24dp"
            android:layout_marginStart="24dp"
            android:textColor="?android:attr/textColorSecondary"
            android:textSize="16sp"
            android:maxLines="4"/>

        <ImageView
            android:id="@+id/fingerprint_icon"
            android:layout_width="@dimen/fingerprint_icon_size"
            android:layout_height="@dimen/fingerprint_icon_size"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="32dp"
            android:scaleType="fitXY" />

        <TextView
            android:id="@+id/fingerprint_error"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginEnd="24dp"
            android:layout_marginStart="24dp"
            android:paddingTop="16dp"
            android:paddingBottom="24dp"
            android:textSize="14sp"
            android:gravity="center_horizontal"
            android:accessibilityLiveRegion="polite"
            android:text="@string/fingerprint_dialog_touch_sensor"
            android:textColor="?android:attr/textColorSecondary" />

    </LinearLayout>

</ScrollView>
