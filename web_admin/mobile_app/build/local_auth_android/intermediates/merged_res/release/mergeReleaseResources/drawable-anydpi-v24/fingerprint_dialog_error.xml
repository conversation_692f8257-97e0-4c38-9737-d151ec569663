<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ Copyright 2018 The Android Open Source Project
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License");
  ~ you may not use this file except in compliance with the License.
  ~ You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->

<vector xmlns:android="http://schemas.android.com/apk/res/android" android:width="24dp"
        android:height="24dp" android:viewportHeight="24" android:viewportWidth="24">
    <group android:name="_R_G">
        <group android:name="_R_G_L_1_G" android:rotation="10" android:translateX="12"
               android:translateY="12">
            <path android:name="_R_G_L_1_G_D_0_P_0" android:pathData=" M0 -9 C4.97,-9 9,-4.97 9,0 C9,4.97 4.97,9 0,9 C-4.97,9 -9,4.97 -9,0 C-9,-4.97 -4.97,-9 0,-9c "
                  android:strokeAlpha="1" android:strokeColor="#ff5722"
                  android:strokeLineCap="round" android:strokeLineJoin="round" android:strokeWidth="2"
                  android:trimPathEnd="1" android:trimPathOffset="0"
                  android:trimPathStart="0"/>
        </group>
        <group android:name="_R_G_L_0_G" android:translateX="12" android:translateY="12">
            <group android:name="_R_G_L_0_G_D_0_P_0_G_0_T_0" android:pivotY="-0.012"
                   android:rotation="0" android:scaleX="1" android:scaleY="1">
                <path android:name="_R_G_L_0_G_D_0_P_0" android:fillAlpha="1"
                      android:fillColor="#ff5722" android:fillType="nonZero"
                      android:pathData=" M1.1 3.94 C1.1,4.55 0.61,5.04 0,5.04 C-0.61,5.04 -1.1,4.55 -1.1,3.94 C-1.1,3.33 -0.61,2.84 0,2.84 C0.61,2.84 1.1,3.33 1.1,3.94c "/>
            </group>
            <group android:name="_R_G_L_0_G_D_0_P_1_G_0_T_0" android:pivotY="-0.012"
                   android:rotation="0" android:scaleX="1" android:scaleY="1">
                <path android:name="_R_G_L_0_G_D_0_P_1" android:fillAlpha="1"
                      android:fillColor="#ff5722" android:fillType="nonZero"
                      android:pathData=" M1 -4.06 C1,-4.06 1,-0.06 1,-0.06 C1,0.49 0.55,0.94 0,0.94 C-0.55,0.94 -1,0.49 -1,-0.06 C-1,-0.06 -1,-4.06 -1,-4.06 C-1,-4.61 -0.55,-5.06 0,-5.06 C0.55,-5.06 1,-4.61 1,-4.06c "/>
            </group>
        </group>
    </group>
</vector>