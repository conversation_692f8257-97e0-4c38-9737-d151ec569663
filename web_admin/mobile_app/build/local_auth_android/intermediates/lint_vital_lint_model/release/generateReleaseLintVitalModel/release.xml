<variant
    name="release"
    package="io.flutter.plugins.localauth"
    minSdkVersion="21"
    targetSdkVersion="21"
    mergedManifest="/Volumes/sathish/mcq/mobile_app/build/local_auth_android/intermediates/merged_manifest/release/processReleaseManifest/AndroidManifest.xml"
    manifestMergeReport="/Volumes/sathish/mcq/mobile_app/build/local_auth_android/outputs/logs/manifest-merger-release-report.txt"
    proguardFiles="/Volumes/sathish/mcq/mobile_app/build/local_auth_android/intermediates/default_proguard_files/global/proguard-android.txt-8.3.0"
    partialResultsDir="/Volumes/sathish/mcq/mobile_app/build/local_auth_android/intermediates/lint_vital_partial_results/release/lintVitalAnalyzeRelease/out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src/main/AndroidManifest.xml"
        javaDirectories="src/main/java:src/release/java:src/main/kotlin:src/release/kotlin"
        resDirectories="src/main/res:src/release/res"
        assetsDirectories="src/main/assets:src/release/assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="/Volumes/sathish/mcq/mobile_app/build/local_auth_android/intermediates/javac/release/compileReleaseJavaWithJavac/classes:/Volumes/sathish/mcq/mobile_app/build/local_auth_android/intermediates/compile_r_class_jar/release/generateReleaseRFile/R.jar"
      type="MAIN"
      applicationId="io.flutter.plugins.localauth"
      generatedSourceFolders="/Volumes/sathish/mcq/mobile_app/build/local_auth_android/generated/ap_generated_sources/release/out:/Volumes/sathish/mcq/mobile_app/build/local_auth_android/generated/source/buildConfig/release"
      generatedResourceFolders="/Volumes/sathish/mcq/mobile_app/build/local_auth_android/generated/res/resValues/release"
      desugaredMethodsFiles="/Users/<USER>/.gradle/caches/8.12/transforms/9acce8ac0dbb31560a259830e46ca38a/transformed/D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
