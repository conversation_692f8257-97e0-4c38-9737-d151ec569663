<lint-module
    format="1"
    dir="/Users/<USER>/.pub-cache/hosted/pub.dev/local_auth_android-1.0.49/android"
    name=":local_auth_android"
    type="LIBRARY"
    maven="io.flutter.plugins.localauth:local_auth_android:"
    agpVersion="8.3.0"
    buildFolder="/Volumes/sathish/mcq/mobile_app/build/local_auth_android"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-35/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="11"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      disable="AndroidGradlePluginVersion,InvalidPackage,GradleDependency,NewerVersionAvailable"
      abortOnError="true"
      absolutePaths="true"
      checkAllWarnings="true"
      warningsAsErrors="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="AndroidGradlePluginVersion"
        severity="IGNORE" />
      <severity
        id="GradleDependency"
        severity="IGNORE" />
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
      <severity
        id="NewerVersionAvailable"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
