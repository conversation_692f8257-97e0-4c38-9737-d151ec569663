{"logs": [{"outputFile": "io.flutter.plugins.localauth.local_auth_android-release-29:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/01a53bb2de81ca487d2d487a771122d8/transformed/appcompat-1.2.0/res/values-en-rIN/values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,4924", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,5002"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-en-rIN/values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "29,30,31,32,33,34,35,49", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2762,2858,2960,3059,3158,3262,3365,5007", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "2853,2955,3054,3153,3257,3360,3476,5103"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/443bd66a5019fb5509900300d05d9035/transformed/biometric-1.1.0/res/values-en-rIN/values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,370,499,637,764,882,1013,1113,1239,1378", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "156,246,365,494,632,759,877,1008,1108,1234,1373,1493"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3481,3587,3677,3796,3925,4063,4190,4308,4439,4539,4665,4804", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "3582,3672,3791,3920,4058,4185,4303,4434,4534,4660,4799,4919"}}]}, {"outputFile": "io.flutter.plugins.localauth.local_auth_android-mergeReleaseResources-27:/values-en-rIN/values-en-rIN.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/01a53bb2de81ca487d2d487a771122d8/transformed/appcompat-1.2.0/res/values-en-rIN/values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,2762", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,2840"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,209,309,417,501,601,716,794,869,960,1053,1148,1242,1342,1435,1530,1624,1715,1806,1888,1991,2094,2193,2298,2402,2506,2662,4924", "endColumns": "103,99,107,83,99,114,77,74,90,92,94,93,99,92,94,93,90,90,81,102,102,98,104,103,103,155,99,82", "endOffsets": "204,304,412,496,596,711,789,864,955,1048,1143,1237,1337,1430,1525,1619,1710,1801,1883,1986,2089,2188,2293,2397,2501,2657,2757,5002"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-en-rIN/values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "29,30,31,32,33,34,35,49", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2762,2858,2960,3059,3158,3262,3365,5007", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "2853,2955,3054,3153,3257,3360,3476,5103"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/443bd66a5019fb5509900300d05d9035/transformed/biometric-1.1.0/res/values-en-rIN/values-en-rIN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,161,251,370,499,637,764,882,1013,1113,1239,1378", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "156,246,365,494,632,759,877,1008,1108,1234,1373,1493"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3481,3587,3677,3796,3925,4063,4190,4308,4439,4539,4665,4804", "endColumns": "105,89,118,128,137,126,117,130,99,125,138,119", "endOffsets": "3582,3672,3791,3920,4058,4185,4303,4434,4534,4660,4799,4919"}}]}]}