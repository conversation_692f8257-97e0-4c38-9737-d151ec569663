{"logs": [{"outputFile": "io.flutter.plugins.localauth.local_auth_android-release-29:/values-sr/values-sr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/01a53bb2de81ca487d2d487a771122d8/transformed/appcompat-1.2.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,5039", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,5121"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "29,30,31,32,33,34,35,49", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2833,2931,3033,3130,3234,3338,3443,5126", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "2926,3028,3125,3229,3333,3438,3554,5222"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/443bd66a5019fb5509900300d05d9035/transformed/biometric-1.1.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,259,380,514,645,772,903,1037,1137,1276,1409", "endColumns": "110,92,120,133,130,126,130,133,99,138,132,125", "endOffsets": "161,254,375,509,640,767,898,1032,1132,1271,1404,1530"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3559,3670,3763,3884,4018,4149,4276,4407,4541,4641,4780,4913", "endColumns": "110,92,120,133,130,126,130,133,99,138,132,125", "endOffsets": "3665,3758,3879,4013,4144,4271,4402,4536,4636,4775,4908,5034"}}]}, {"outputFile": "io.flutter.plugins.localauth.local_auth_android-mergeReleaseResources-27:/values-sr/values-sr.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/01a53bb2de81ca487d2d487a771122d8/transformed/appcompat-1.2.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,5039", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,5121"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "29,30,31,32,33,34,35,49", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2833,2931,3033,3130,3234,3338,3443,5126", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "2926,3028,3125,3229,3333,3438,3554,5222"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/443bd66a5019fb5509900300d05d9035/transformed/biometric-1.1.0/res/values-sr/values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,166,259,380,514,645,772,903,1037,1137,1276,1409", "endColumns": "110,92,120,133,130,126,130,133,99,138,132,125", "endOffsets": "161,254,375,509,640,767,898,1032,1132,1271,1404,1530"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3559,3670,3763,3884,4018,4149,4276,4407,4541,4641,4780,4913", "endColumns": "110,92,120,133,130,126,130,133,99,138,132,125", "endOffsets": "3665,3758,3879,4013,4144,4271,4402,4536,4636,4775,4908,5034"}}]}]}