{"logs": [{"outputFile": "io.flutter.plugins.localauth.local_auth_android-release-29:/values-land/values-land.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/01a53bb2de81ca487d2d487a771122d8/transformed/appcompat-1.2.0/res/values-land/values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}]}, {"outputFile": "io.flutter.plugins.localauth.local_auth_android-mergeReleaseResources-27:/values-land/values-land.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/01a53bb2de81ca487d2d487a771122d8/transformed/appcompat-1.2.0/res/values-land/values-land.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,125,196", "endColumns": "69,70,67", "endOffsets": "120,191,259"}}]}]}