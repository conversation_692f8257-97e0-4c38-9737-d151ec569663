{"logs": [{"outputFile": "io.flutter.plugins.localauth.local_auth_android-release-29:/values-my/values-my.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/443bd66a5019fb5509900300d05d9035/transformed/biometric-1.1.0/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,260,380,501,635,772,902,1055,1144,1294,1437", "endColumns": "108,95,119,120,133,136,129,152,88,149,142,136", "endOffsets": "159,255,375,496,630,767,897,1050,1139,1289,1432,1569"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3614,3723,3819,3939,4060,4194,4331,4461,4614,4703,4853,4996", "endColumns": "108,95,119,120,133,136,129,152,88,149,142,136", "endOffsets": "3718,3814,3934,4055,4189,4326,4456,4609,4698,4848,4991,5128"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "29,30,31,32,33,34,35,49", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2872,2975,3079,3182,3284,3389,3495,5219", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "2970,3074,3177,3279,3384,3490,3609,5315"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/01a53bb2de81ca487d2d487a771122d8/transformed/appcompat-1.2.0/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,842,923,1014,1107,1202,1296,1396,1489,1584,1678,1769,1860,1945,2060,2169,2268,2394,2501,2609,2769,2872", "endColumns": "112,106,115,86,108,122,81,80,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,837,918,1009,1102,1197,1291,1391,1484,1579,1673,1764,1855,1940,2055,2164,2263,2389,2496,2604,2764,2867,2953"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,842,923,1014,1107,1202,1296,1396,1489,1584,1678,1769,1860,1945,2060,2169,2268,2394,2501,2609,2769,5133", "endColumns": "112,106,115,86,108,122,81,80,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,837,918,1009,1102,1197,1291,1391,1484,1579,1673,1764,1855,1940,2055,2164,2263,2389,2496,2604,2764,2867,5214"}}]}, {"outputFile": "io.flutter.plugins.localauth.local_auth_android-mergeReleaseResources-27:/values-my/values-my.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/443bd66a5019fb5509900300d05d9035/transformed/biometric-1.1.0/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,164,260,380,501,635,772,902,1055,1144,1294,1437", "endColumns": "108,95,119,120,133,136,129,152,88,149,142,136", "endOffsets": "159,255,375,496,630,767,897,1050,1139,1289,1432,1569"}, "to": {"startLines": "36,37,38,39,40,41,42,43,44,45,46,47", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3614,3723,3819,3939,4060,4194,4331,4461,4614,4703,4853,4996", "endColumns": "108,95,119,120,133,136,129,152,88,149,142,136", "endOffsets": "3718,3814,3934,4055,4189,4326,4456,4609,4698,4848,4991,5128"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "29,30,31,32,33,34,35,49", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2872,2975,3079,3182,3284,3389,3495,5219", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "2970,3074,3177,3279,3384,3490,3609,5315"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/01a53bb2de81ca487d2d487a771122d8/transformed/appcompat-1.2.0/res/values-my/values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,842,923,1014,1107,1202,1296,1396,1489,1584,1678,1769,1860,1945,2060,2169,2268,2394,2501,2609,2769,2872", "endColumns": "112,106,115,86,108,122,81,80,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,837,918,1009,1102,1197,1291,1391,1484,1579,1673,1764,1855,1940,2055,2164,2263,2389,2496,2604,2764,2867,2953"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,48", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,842,923,1014,1107,1202,1296,1396,1489,1584,1678,1769,1860,1945,2060,2169,2268,2394,2501,2609,2769,5133", "endColumns": "112,106,115,86,108,122,81,80,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,837,918,1009,1102,1197,1291,1391,1484,1579,1673,1764,1855,1940,2055,2164,2263,2389,2496,2604,2764,2867,5214"}}]}]}