{"logs": [{"outputFile": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/values-as/values-as.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,3355", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,3451"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/65e14b9341fd3254c0b93be8370491de/transformed/browser-1.4.0/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,269,377", "endColumns": "107,105,107,105", "endOffsets": "158,264,372,478"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2927,3035,3141,3249", "endColumns": "107,105,107,105", "endOffsets": "3030,3136,3244,3350"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4c34c7620a6896dcd812c18c39f48466/transformed/jetified-play-services-basement-18.3.0/res/values-as/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "121", "endOffsets": "316"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1776", "endColumns": "125", "endOffsets": "1897"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e8fc0174e59bb3ec21deb0ac3321630b/transformed/jetified-play-services-base-18.0.1/res/values-as/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,446,565,671,797,915,1024,1132,1271,1376,1522,1643,1772,1921,1977,2039", "endColumns": "103,148,118,105,125,117,108,107,138,104,145,120,128,148,55,61,81", "endOffsets": "296,445,564,670,796,914,1023,1131,1270,1375,1521,1642,1771,1920,1976,2038,2120"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "805,913,1066,1189,1299,1429,1551,1664,1902,2045,2154,2304,2429,2562,2715,2775,2841", "endColumns": "107,152,122,109,129,121,112,111,142,108,149,124,132,152,59,65,85", "endOffsets": "908,1061,1184,1294,1424,1546,1659,1771,2040,2149,2299,2424,2557,2710,2770,2836,2922"}}]}, {"outputFile": "io.flutter.plugins.firebase.auth.firebase_auth-mergeReleaseResources-27:/values-as/values-as.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,805", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,901"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,156,259,367,472,576,676,3355", "endColumns": "100,102,107,104,103,99,128,100", "endOffsets": "151,254,362,467,571,671,800,3451"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/65e14b9341fd3254c0b93be8370491de/transformed/browser-1.4.0/res/values-as/values-as.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,269,377", "endColumns": "107,105,107,105", "endOffsets": "158,264,372,478"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2927,3035,3141,3249", "endColumns": "107,105,107,105", "endOffsets": "3030,3136,3244,3350"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4c34c7620a6896dcd812c18c39f48466/transformed/jetified-play-services-basement-18.3.0/res/values-as/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "121", "endOffsets": "316"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1776", "endColumns": "125", "endOffsets": "1897"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e8fc0174e59bb3ec21deb0ac3321630b/transformed/jetified-play-services-base-18.0.1/res/values-as/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,446,565,671,797,915,1024,1132,1271,1376,1522,1643,1772,1921,1977,2039", "endColumns": "103,148,118,105,125,117,108,107,138,104,145,120,128,148,55,61,81", "endOffsets": "296,445,564,670,796,914,1023,1131,1270,1375,1521,1642,1771,1920,1976,2038,2120"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "805,913,1066,1189,1299,1429,1551,1664,1902,2045,2154,2304,2429,2562,2715,2775,2841", "endColumns": "107,152,122,109,129,121,112,111,142,108,149,124,132,152,59,65,85", "endOffsets": "908,1061,1184,1294,1424,1546,1659,1771,2040,2149,2299,2424,2557,2710,2770,2836,2922"}}]}]}