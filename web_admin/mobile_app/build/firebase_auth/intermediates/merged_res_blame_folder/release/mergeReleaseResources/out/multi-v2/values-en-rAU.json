{"logs": [{"outputFile": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,1177", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,1273"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/65e14b9341fd3254c0b93be8370491de/transformed/browser-1.4.0/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "774,872,969,1078", "endColumns": "97,96,108,98", "endOffsets": "867,964,1073,1172"}}]}, {"outputFile": "io.flutter.plugins.firebase.auth.firebase_auth-mergeReleaseResources-27:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "2,3,4,5,6,7,8,13", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,1177", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,1273"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/65e14b9341fd3254c0b93be8370491de/transformed/browser-1.4.0/res/values-en-rAU/values-en-rAU.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "9,10,11,12", "startColumns": "4,4,4,4", "startOffsets": "774,872,969,1078", "endColumns": "97,96,108,98", "endOffsets": "867,964,1073,1172"}}]}]}