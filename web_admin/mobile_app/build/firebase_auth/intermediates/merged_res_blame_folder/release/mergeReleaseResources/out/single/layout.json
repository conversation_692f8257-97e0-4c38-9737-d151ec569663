[{"merged": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/layout/notification_template_custom_big.xml", "source": "io.flutter.plugins.firebase.auth.firebase_auth-core-1.13.1-19:/layout/notification_template_custom_big.xml"}, {"merged": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/layout/notification_action.xml", "source": "io.flutter.plugins.firebase.auth.firebase_auth-core-1.13.1-19:/layout/notification_action.xml"}, {"merged": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/layout/browser_actions_context_menu_row.xml", "source": "io.flutter.plugins.firebase.auth.firebase_auth-browser-1.4.0-9:/layout/browser_actions_context_menu_row.xml"}, {"merged": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/layout/notification_action_tombstone.xml", "source": "io.flutter.plugins.firebase.auth.firebase_auth-core-1.13.1-19:/layout/notification_action_tombstone.xml"}, {"merged": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/layout/ime_secondary_split_test_activity.xml", "source": "io.flutter.plugins.firebase.auth.firebase_auth-core-1.13.1-19:/layout/ime_secondary_split_test_activity.xml"}, {"merged": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/layout/notification_template_part_chronometer.xml", "source": "io.flutter.plugins.firebase.auth.firebase_auth-core-1.13.1-19:/layout/notification_template_part_chronometer.xml"}, {"merged": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/layout/ime_base_split_test_activity.xml", "source": "io.flutter.plugins.firebase.auth.firebase_auth-core-1.13.1-19:/layout/ime_base_split_test_activity.xml"}, {"merged": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/layout/notification_template_icon_group.xml", "source": "io.flutter.plugins.firebase.auth.firebase_auth-core-1.13.1-19:/layout/notification_template_icon_group.xml"}, {"merged": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/layout/notification_template_part_time.xml", "source": "io.flutter.plugins.firebase.auth.firebase_auth-core-1.13.1-19:/layout/notification_template_part_time.xml"}, {"merged": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/layout/browser_actions_context_menu_page.xml", "source": "io.flutter.plugins.firebase.auth.firebase_auth-browser-1.4.0-9:/layout/browser_actions_context_menu_page.xml"}, {"merged": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/layout/custom_dialog.xml", "source": "io.flutter.plugins.firebase.auth.firebase_auth-core-1.13.1-19:/layout/custom_dialog.xml"}]