{"logs": [{"outputFile": "io.flutter.plugins.firebase.auth.firebase_auth-mergeReleaseResources-27:/values-iw/values-iw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,3222", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,3318"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/65e14b9341fd3254c0b93be8370491de/transformed/browser-1.4.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2823,2914,3014,3120", "endColumns": "90,99,105,101", "endOffsets": "2909,3009,3115,3217"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e8fc0174e59bb3ec21deb0ac3321630b/transformed/jetified-play-services-base-18.0.1/res/values-iw/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "752,855,1009,1134,1238,1377,1502,1614,1835,1971,2075,2220,2343,2477,2622,2682,2742", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "850,1004,1129,1233,1372,1497,1609,1712,1966,2070,2215,2338,2472,2617,2677,2737,2818"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4c34c7620a6896dcd812c18c39f48466/transformed/jetified-play-services-basement-18.3.0/res/values-iw/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1717", "endColumns": "117", "endOffsets": "1830"}}]}, {"outputFile": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/values-iw/values-iw.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,752", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,848"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,445,546,646,3222", "endColumns": "93,101,96,96,100,99,105,100", "endOffsets": "144,246,343,440,541,641,747,3318"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/65e14b9341fd3254c0b93be8370491de/transformed/browser-1.4.0/res/values-iw/values-iw.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,146,246,352", "endColumns": "90,99,105,101", "endOffsets": "141,241,347,449"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "2823,2914,3014,3120", "endColumns": "90,99,105,101", "endOffsets": "2909,3009,3115,3217"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e8fc0174e59bb3ec21deb0ac3321630b/transformed/jetified-play-services-base-18.0.1/res/values-iw/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,442,563,663,798,919,1027,1126,1258,1358,1499,1618,1748,1889,1945,2001", "endColumns": "98,149,120,99,134,120,107,98,131,99,140,118,129,140,55,55,76", "endOffsets": "291,441,562,662,797,918,1026,1125,1257,1357,1498,1617,1747,1888,1944,2000,2077"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "752,855,1009,1134,1238,1377,1502,1614,1835,1971,2075,2220,2343,2477,2622,2682,2742", "endColumns": "102,153,124,103,138,124,111,102,135,103,144,122,133,144,59,59,80", "endOffsets": "850,1004,1129,1233,1372,1497,1609,1712,1966,2070,2215,2338,2472,2617,2677,2737,2818"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4c34c7620a6896dcd812c18c39f48466/transformed/jetified-play-services-basement-18.3.0/res/values-iw/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "113", "endOffsets": "308"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1717", "endColumns": "117", "endOffsets": "1830"}}]}]}