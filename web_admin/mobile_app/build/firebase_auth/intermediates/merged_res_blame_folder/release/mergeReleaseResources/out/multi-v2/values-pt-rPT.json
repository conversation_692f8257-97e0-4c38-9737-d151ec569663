{"logs": [{"outputFile": "io.flutter.plugins.firebase.auth.firebase_auth-mergeReleaseResources-27:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/65e14b9341fd3254c0b93be8370491de/transformed/browser-1.4.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3121,3237,3336,3448", "endColumns": "115,98,111,102", "endOffsets": "3232,3331,3443,3546"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,3551", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,3647"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4c34c7620a6896dcd812c18c39f48466/transformed/jetified-play-services-basement-18.3.0/res/values-pt-rPT/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1818", "endColumns": "144", "endOffsets": "1958"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e8fc0174e59bb3ec21deb0ac3321630b/transformed/jetified-play-services-base-18.0.1/res/values-pt-rPT/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,892,1055,1183,1291,1459,1587,1709,1963,2151,2259,2429,2560,2719,2897,2965,3034", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "887,1050,1178,1286,1454,1582,1704,1813,2146,2254,2424,2555,2714,2892,2960,3029,3116"}}]}, {"outputFile": "io.flutter.plugins.firebase.auth.firebase_auth-release-29:/values-pt-rPT/values-pt-rPT.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.12/transforms/65e14b9341fd3254c0b93be8370491de/transformed/browser-1.4.0/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,171,270,382", "endColumns": "115,98,111,102", "endOffsets": "166,265,377,480"}, "to": {"startLines": "27,28,29,30", "startColumns": "4,4,4,4", "startOffsets": "3121,3237,3336,3448", "endColumns": "115,98,111,102", "endOffsets": "3232,3331,3443,3546"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e5fbf39bdbf8c903bac0e6a6b21984dd/transformed/core-1.13.1/res/values-pt-rPT/values-pt-rPT.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,787", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,883"}, "to": {"startLines": "2,3,4,5,6,7,8,31", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,666,3551", "endColumns": "96,101,98,99,106,105,120,100", "endOffsets": "147,249,348,448,555,661,782,3647"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/4c34c7620a6896dcd812c18c39f48466/transformed/jetified-play-services-basement-18.3.0/res/values-pt-rPT/values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "140", "endOffsets": "339"}, "to": {"startLines": "17", "startColumns": "4", "startOffsets": "1818", "endColumns": "144", "endOffsets": "1958"}}, {"source": "/Users/<USER>/.gradle/caches/8.12/transforms/e8fc0174e59bb3ec21deb0ac3321630b/transformed/jetified-play-services-base-18.0.1/res/values-pt-rPT/values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,298,457,581,685,849,973,1091,1196,1380,1484,1650,1777,1932,2106,2170,2235", "endColumns": "100,158,123,103,163,123,117,104,183,103,165,126,154,173,63,64,82", "endOffsets": "297,456,580,684,848,972,1090,1195,1379,1483,1649,1776,1931,2105,2169,2234,2317"}, "to": {"startLines": "9,10,11,12,13,14,15,16,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "787,892,1055,1183,1291,1459,1587,1709,1963,2151,2259,2429,2560,2719,2897,2965,3034", "endColumns": "104,162,127,107,167,127,121,108,187,107,169,130,158,177,67,68,86", "endOffsets": "887,1050,1178,1286,1454,1582,1704,1813,2146,2254,2424,2555,2714,2892,2960,3029,3116"}}]}]}