<lint-module
    format="1"
    dir="/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_auth-4.20.0/android"
    name=":firebase_auth"
    type="LIBRARY"
    maven="io.flutter.plugins.firebase.auth:firebase_auth:"
    agpVersion="8.3.0"
    buildFolder="/Volumes/sathish/mcq/mobile_app/build/firebase_auth"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-34/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-34"
    neverShrinking="true">
  <lintOptions
      disable="InvalidPackage"
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
