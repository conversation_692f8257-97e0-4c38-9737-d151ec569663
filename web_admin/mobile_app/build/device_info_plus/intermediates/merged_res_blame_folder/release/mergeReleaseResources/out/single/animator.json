[{"merged": "dev.fluttercommunity.plus.device_info.device_info_plus-release-25:/animator/fragment_close_enter.xml", "source": "dev.fluttercommunity.plus.device_info.device_info_plus-fragment-1.7.1-5:/animator/fragment_close_enter.xml"}, {"merged": "dev.fluttercommunity.plus.device_info.device_info_plus-release-25:/animator/fragment_open_enter.xml", "source": "dev.fluttercommunity.plus.device_info.device_info_plus-fragment-1.7.1-5:/animator/fragment_open_enter.xml"}, {"merged": "dev.fluttercommunity.plus.device_info.device_info_plus-release-25:/animator/fragment_fade_exit.xml", "source": "dev.fluttercommunity.plus.device_info.device_info_plus-fragment-1.7.1-5:/animator/fragment_fade_exit.xml"}, {"merged": "dev.fluttercommunity.plus.device_info.device_info_plus-release-25:/animator/fragment_close_exit.xml", "source": "dev.fluttercommunity.plus.device_info.device_info_plus-fragment-1.7.1-5:/animator/fragment_close_exit.xml"}, {"merged": "dev.fluttercommunity.plus.device_info.device_info_plus-release-25:/animator/fragment_fade_enter.xml", "source": "dev.fluttercommunity.plus.device_info.device_info_plus-fragment-1.7.1-5:/animator/fragment_fade_enter.xml"}, {"merged": "dev.fluttercommunity.plus.device_info.device_info_plus-release-25:/animator/fragment_open_exit.xml", "source": "dev.fluttercommunity.plus.device_info.device_info_plus-fragment-1.7.1-5:/animator/fragment_open_exit.xml"}]