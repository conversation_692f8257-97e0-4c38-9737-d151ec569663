<lint-module
    format="1"
    dir="/Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-9.1.2/android"
    name=":device_info_plus"
    type="LIBRARY"
    maven="dev.fluttercommunity.plus.device_info:device_info_plus:"
    agpVersion="8.3.0"
    buildFolder="/Volumes/sathish/mcq/mobile_app/build/device_info_plus"
    bootClassPath="/Users/<USER>/Library/Android/sdk/platforms/android-33/android.jar:/Users/<USER>/Library/Android/sdk/build-tools/34.0.0/core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-33"
    neverShrinking="true">
  <lintOptions
      disable="InvalidPackage"
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
