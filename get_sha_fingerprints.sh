#!/bin/bash

# <PERSON>ript to get SHA fingerprints for Firebase configuration

echo "🔐 Getting SHA Fingerprints for Firebase Configuration"
echo "=================================================="

# Check if keytool is available
if ! command -v keytool &> /dev/null; then
    echo "❌ keytool not found. Please install Java JDK."
    echo "   Download from: https://www.oracle.com/java/technologies/downloads/"
    exit 1
fi

echo ""
echo "📱 Debug SHA-1 Fingerprint:"
echo "----------------------------"

# Get debug keystore path
DEBUG_KEYSTORE="$HOME/.android/debug.keystore"

if [ -f "$DEBUG_KEYSTORE" ]; then
    echo "🔍 Found debug keystore at: $DEBUG_KEYSTORE"
    echo ""
    
    # Extract SHA-1 fingerprint
    SHA1=$(keytool -list -v -alias androiddebugkey -keystore "$DEBUG_KEYSTORE" -storepass android -keypass android 2>/dev/null | grep "SHA1:" | cut -d' ' -f3)
    
    if [ -n "$SHA1" ]; then
        echo "✅ Debug SHA-1: $SHA1"
        echo ""
        echo "📋 Copy this SHA-1 fingerprint to Firebase Console:"
        echo "   1. Go to Firebase Console → Project Settings → General"
        echo "   2. Select your Android app"
        echo "   3. Click 'Add fingerprint'"
        echo "   4. Paste: $SHA1"
    else
        echo "❌ Could not extract SHA-1 fingerprint"
    fi
else
    echo "❌ Debug keystore not found at: $DEBUG_KEYSTORE"
    echo "   Run 'flutter build apk --debug' first to generate it"
fi

echo ""
echo "🏗️ Release SHA-1 Fingerprint:"
echo "------------------------------"

# Check for release keystore
RELEASE_KEYSTORE="android/app/upload-keystore.jks"
if [ -f "$RELEASE_KEYSTORE" ]; then
    echo "🔍 Found release keystore at: $RELEASE_KEYSTORE"
    echo "⚠️  You'll need to provide the keystore password and alias"
    echo "   Run manually: keytool -list -v -alias <your-alias> -keystore $RELEASE_KEYSTORE"
else
    echo "ℹ️  No release keystore found. This is normal for development."
    echo "   Create one when ready for production release."
fi

echo ""
echo "🔧 Additional Steps:"
echo "-------------------"
echo "1. Add the SHA-1 fingerprint to Firebase Console"
echo "2. Download updated google-services.json"
echo "3. Replace android/app/google-services.json"
echo "4. Clean and rebuild: flutter clean && flutter build apk"
echo ""
echo "📚 For more help, see: FIREBASE_CONFIGURATION_FIX.md"
