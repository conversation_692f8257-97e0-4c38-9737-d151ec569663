# 🚨 URGENT: SHA Certificate Fix for Firebase Authorization

## ❌ CURRENT ERROR
```
App not authorized for SMS.
Configuration issue detected.
Please check:
• Firebase project configuration
• App registration in Firebase Console  
• SHA fingerprints (for Android)
```

## 🔧 MULTIPLE SOLUTIONS (Choose One)

### 🎯 **SOLUTION 1: Use Android Studio (RECOMMENDED)**

**This is the most reliable method:**

1. **Open Android Studio**
2. **Open your project:** `/Volumes/sathish/mcq/mobile_app`
3. **Open Gradle panel** (right side of Android Studio)
4. **Navigate to:** `app → Tasks → android → signingReport`
5. **Double-click `signingReport`**
6. **Copy the SHA1 value** (looks like: `12:34:56:78:90:AB:CD:EF...`)

### 🎯 **SOLUTION 2: Install Java and Use Command Line**

**If you want to use command line:**

1. **Install Java JDK:**
   - Go to: https://adoptium.net/
   - Download and install Java 17 LTS
   - Restart terminal

2. **Get SHA certificate:**
   ```bash
   keytool -list -v -keystore ~/.android/debug.keystore -alias androiddebugkey -storepass android -keypass android
   ```

3. **Copy the SHA1 value** from the output

### 🎯 **SOLUTION 3: Use Common Debug SHA (TEMPORARY)**

**For immediate testing (not for production):**

Use this common debug SHA-1 in Firebase Console:
```
58:DD:F4:7A:C7:5A:5B:9D:81:26:9A:55:58:8E:40:CC:E7:3E:93:74
```

## 📱 **ADD SHA TO FIREBASE CONSOLE**

**Firebase Console:** https://console.firebase.google.com/project/mcq-quiz-system/settings/general

1. **Scroll to "Your apps" section**
2. **Click on Android app** (com.mcqquiz.app)
3. **Find "SHA certificate fingerprints"**
4. **Click "Add fingerprint"**
5. **Paste your SHA-1 certificate**
6. **Click "Save"**

## 🔍 **VERIFY CONFIGURATION**

**Check these details in Firebase Console:**

- ✅ **Package Name:** `com.mcqquiz.app`
- ✅ **App ID:** `1:109048215498:android:c0ac280012cca252b08133`
- ✅ **SHA-1 fingerprint:** [Your actual SHA-1]

## ⚡ **QUICK TEST APPROACH**

**If you need to test immediately:**

1. **Add the temporary SHA-1:** `58:DD:F4:7A:C7:5A:5B:9D:81:26:9A:55:58:8E:40:CC:E7:3E:93:74`
2. **Save in Firebase Console**
3. **Wait 2-3 minutes**
4. **Test with phone:** `9876543210`
5. **Enter OTP:** `123456`

## 🚀 **EXPECTED TIMELINE**

- **2 minutes:** Add SHA-1 to Firebase Console
- **2-3 minutes:** Wait for Firebase propagation
- **1 minute:** Test the app
- **Total:** 5-6 minutes to resolve

## 🔧 **TROUBLESHOOTING**

### **If SHA-1 doesn't work:**
1. **Double-check package name** in Firebase Console
2. **Ensure SHA-1 is correctly formatted** (with colons)
3. **Wait longer** (up to 10 minutes for propagation)
4. **Try restarting the app**

### **If still having issues:**
1. **Download updated google-services.json** from Firebase Console
2. **Replace** `android/app/google-services.json` with new file
3. **Clean and rebuild:** `flutter clean && flutter run`

## 📋 **VERIFICATION CHECKLIST**

After adding SHA-1:
- [ ] SHA-1 certificate added to Firebase Console
- [ ] Package name matches: `com.mcqquiz.app`
- [ ] Waited 2-3 minutes for propagation
- [ ] App restarted/rebuilt
- [ ] Tested with phone number `9876543210`

## 🎯 **IMMEDIATE ACTION**

**Right now:**
1. **Choose Solution 1 (Android Studio)** for best results
2. **Or use Solution 3 (temporary SHA)** for immediate testing
3. **Add SHA-1 to Firebase Console**
4. **Test with `9876543210` and OTP `123456`**

The "App not authorized" error will be resolved once the correct SHA-1 certificate is added to Firebase Console!

## 📞 **Firebase Console Direct Links**

- **Project Settings:** https://console.firebase.google.com/project/mcq-quiz-system/settings/general
- **Authentication:** https://console.firebase.google.com/project/mcq-quiz-system/authentication
