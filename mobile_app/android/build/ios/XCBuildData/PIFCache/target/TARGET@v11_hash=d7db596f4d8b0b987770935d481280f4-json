{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fd774ffd723424269d745ade24a90920", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/leveldb-library/leveldb-library-prefix.pch", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/leveldb-library/leveldb-library-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/leveldb-library/leveldb-library.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "leveldb", "PRODUCT_NAME": "leveldb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984885ff3633011340082a534647ef0b7f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d88405d2a7e7c586808325d880cfe222", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/leveldb-library/leveldb-library-prefix.pch", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/leveldb-library/leveldb-library-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/leveldb-library/leveldb-library.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "leveldb", "PRODUCT_NAME": "leveldb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982d23df9c564a53925863659bd0c227ea", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d88405d2a7e7c586808325d880cfe222", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/leveldb-library/leveldb-library-prefix.pch", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/leveldb-library/leveldb-library-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/leveldb-library/leveldb-library.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "leveldb", "PRODUCT_NAME": "leveldb", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98791690f40b20677821db8265d727cb66", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c7cb08c358f11d2cdf949c66a7a6619f", "guid": "bfdfe7dc352907fc980b868725387e98d51a79113d61041b0d8fd362e2df331c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98983740c3cd0f854db20f0bcdfff0c6d5", "guid": "bfdfe7dc352907fc980b868725387e9871cbeca17d0d58d6c721ecea288bbed4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d28e64a0d7a64045ebef8a099e10c79b", "guid": "bfdfe7dc352907fc980b868725387e98967507aeb3aacaf8085bcf4816d02c76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc99ad5533abfd2beb0b6c04b552e26b", "guid": "bfdfe7dc352907fc980b868725387e98f25ddb07a6771f02dac1782fc2008148"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df43038d1df80e2608f5ceb155af2186", "guid": "bfdfe7dc352907fc980b868725387e989245a4ef6bbf32df3e7ff907e3b24c6e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984b2851cbb95dd1f8846be0e4d29f25c3", "guid": "bfdfe7dc352907fc980b868725387e98bc77e929c0a93a6f58a7c8f3e2bf2041", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1862373f8eb902799ad622ef03d9a18", "guid": "bfdfe7dc352907fc980b868725387e989f376aca76e5318ea5f7c99685d5a186"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b15bb955bf5ce8dabdd84a8b5012bfa", "guid": "bfdfe7dc352907fc980b868725387e987a733865d374e93bf5586864a7346656", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2b6dbbf755ce48bfdc496d4f2f8ee81", "guid": "bfdfe7dc352907fc980b868725387e98081648ee771c1baffae8cbc0e7c76dbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a154edc1ad764d4c40d7865bbfb32f4", "guid": "bfdfe7dc352907fc980b868725387e98bbc51d11b01b245a99828028f88f5b45", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98565f797dbbc2fe626113c84d4841ffa6", "guid": "bfdfe7dc352907fc980b868725387e988c0bddc194b90e9dcb12cdbb6e5cadc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f79173ef1c05be15c2dc56b5ddf77480", "guid": "bfdfe7dc352907fc980b868725387e988278695a8b9666a0ae946a41b1bb5bf5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cee987d629b16508821fa2e19223d9e6", "guid": "bfdfe7dc352907fc980b868725387e98e6a4f4f8a2a7e6dca32f3dfb5b48e0a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a23681ee2c3606c952b1252aa1b6c0a", "guid": "bfdfe7dc352907fc980b868725387e9889e533bdc43da8559aca82702a90bf20", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980907771abc050fa7a0c19633cc70de70", "guid": "bfdfe7dc352907fc980b868725387e98f82724d6c5a42d88fb651e74ef8c9ccf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cae018c12c7d1676b170a721be325698", "guid": "bfdfe7dc352907fc980b868725387e98da50498c19cbced2d1bdd4a48916071c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984614677f9bdb13de2e0f84ad4fbd109c", "guid": "bfdfe7dc352907fc980b868725387e982c771c9de6d3131a68fdf598682c0106"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98980fe3c928aa24ff3881ed82c03b98c4", "guid": "bfdfe7dc352907fc980b868725387e987650ec8855a5b084f845a2f7e10a83e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7d5588b7761a2ea5566b40ae4cf5669", "guid": "bfdfe7dc352907fc980b868725387e98b60735916c50a4f4cb03fbdc56a903f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984a0f50e86698b6e6e612453d4dbec07b", "guid": "bfdfe7dc352907fc980b868725387e98a95de28687ee2f1ea9123a44acab0511"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbdddceb1ed4d47149190c55570378d0", "guid": "bfdfe7dc352907fc980b868725387e985287fb5eb9568b1dc9b968bef1373a31", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bab8ae9df9be7db6d88ddc420061f284", "guid": "bfdfe7dc352907fc980b868725387e98bbcd6a305469bdb2a2937d133c7ac185"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982075353e27a2f099ede9a32b498ec7c8", "guid": "bfdfe7dc352907fc980b868725387e98cd0538e1380f4925a66efca80dffbcd3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98957ab520ba4620a8c74df1adde75d431", "guid": "bfdfe7dc352907fc980b868725387e985e7586a15ac817e0b8ce37c8c333a1a3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c531b88433f81fe1e4c66e63f1e8cca", "guid": "bfdfe7dc352907fc980b868725387e98a4419da540b51467c6c26b6963efda01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c6f9e3e7f9f75a37d33fabe2e5598c8", "guid": "bfdfe7dc352907fc980b868725387e986177bffc190ddf8ebf01b0c038311a04"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989057c2b318c60f62db5f167863d92624", "guid": "bfdfe7dc352907fc980b868725387e98d58321492d654d1f485130ec4eabcbc1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98536dff8a8b8608c45ecb691d90ef5a56", "guid": "bfdfe7dc352907fc980b868725387e988997eceaf44b7e04d6cc89c5ef32d725"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98834af2659da7165cfadcd3e4032b208d", "guid": "bfdfe7dc352907fc980b868725387e98e6a7cbb8d37efeb9e222c8008d82bf16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ab8e99c707c75a58afff2f6f61a5370", "guid": "bfdfe7dc352907fc980b868725387e98d147d5c0a33dff750404f1e284c9f958"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf1cc8d45779a8662de23cbece5298fb", "guid": "bfdfe7dc352907fc980b868725387e98a5f50225fc62f3b787f76ad2960c9ffd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b2ec81bd32489d8da204ce9ddf9e9570", "guid": "bfdfe7dc352907fc980b868725387e989ba331ca931f1e10da696ab027b20425"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ecea76f0d7f4b021e11d60da47501b4c", "guid": "bfdfe7dc352907fc980b868725387e98ef2feb1b11307545dce8e4cafd2d2c4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4a40cdc1f07f4e14738c5a187b28b06", "guid": "bfdfe7dc352907fc980b868725387e987fa4ffc7044e2ae1c878de78b5dfe2b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f79c3bbc622ae5ba7401bcd62fceabe0", "guid": "bfdfe7dc352907fc980b868725387e981850225dd253c5441ecc8acfff6904ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817d709330611a7f56253376298e21e11", "guid": "bfdfe7dc352907fc980b868725387e98e714d67c0009c29fe958b20c5362074a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988368181800dc21a3c3469cde4f1e5aa1", "guid": "bfdfe7dc352907fc980b868725387e98c9ed29113b45b8e939644101b752c86c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98613a7a6daefa4233593c0311ff7d623f", "guid": "bfdfe7dc352907fc980b868725387e9869020700be470057d5a0dd370cda0949"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98979df64b778b9760f932b7ccbdb8bbd7", "guid": "bfdfe7dc352907fc980b868725387e985c3dbf00c008db36821cb20ed1ea1811"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fef2203a081d4f33dce4a36854ce4d1", "guid": "bfdfe7dc352907fc980b868725387e989d01ca95dacb1e87d16141932b7b63ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882fbe50cf00a7f7a65c73025cfb816fd", "guid": "bfdfe7dc352907fc980b868725387e9857cd2aab4ae1eea2af4b2a975f7ba6a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3b57f8edbbfa39d9a35269d8a2d9a2f", "guid": "bfdfe7dc352907fc980b868725387e98515b13fcf3a723e7e89c8d294024479e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd399657671fbb864c3cea80aeec71af", "guid": "bfdfe7dc352907fc980b868725387e98c92faeffc93842872a7daf4e450bac25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b8d3ecb6dd21f13fe9c9f7c6db359e2", "guid": "bfdfe7dc352907fc980b868725387e98c89d4729c66f2b20cc4b197c98b8a7a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0273f85369398668ac0a942bd1aed28", "guid": "bfdfe7dc352907fc980b868725387e98f6f8356b1c114d517ddb6470abab3831", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b0a21467df72988ec1c749d167ee44e", "guid": "bfdfe7dc352907fc980b868725387e98c6444128f541fedb39b91834d3f8bb36", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98035a516e02f454ba76e0e1503ce973a7", "guid": "bfdfe7dc352907fc980b868725387e980a9175185c7d01481d3e1148f642fb08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e72e6f08e05dd78629e27892d6d36238", "guid": "bfdfe7dc352907fc980b868725387e9837dc549a33bce2db3bc8837fe5e9edd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821ab99e498179882ee26068b870ec248", "guid": "bfdfe7dc352907fc980b868725387e983d7be4247e28f65b52c72b4851651349"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ab9e5a88af3a094be447fe4e68d1e32", "guid": "bfdfe7dc352907fc980b868725387e984a46887ce69344d6339b27a5d60c9a53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98568eb911dc06ca20acb5b2fee5b3adee", "guid": "bfdfe7dc352907fc980b868725387e9847bd8e7b57c94ce25204fd407ea0654f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982806ccdaf58ea55a775252f6b7b283c4", "guid": "bfdfe7dc352907fc980b868725387e98c71894143dab0faebd25c282d2cf5cb1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98635d3286faf56efe2ab17b937c93af77", "guid": "bfdfe7dc352907fc980b868725387e9875940bb6afd697f3cceb363b72077aea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd85b913935d1ad97dbc41e8285fc1a8", "guid": "bfdfe7dc352907fc980b868725387e988470bf6991d32353a1726c9b4bac45b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98025d9c6519c31d8e7f7ca40f8ccb56fb", "guid": "bfdfe7dc352907fc980b868725387e98d2158653ca9fd3c33647e90dfcf66d40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987baedfc908ebdc4f7a61727b522153eb", "guid": "bfdfe7dc352907fc980b868725387e98a7778157418a65a290585e40a3f005fe"}], "guid": "bfdfe7dc352907fc980b868725387e98d63b1c5620f2089a5423e54e004f696c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9860e4ecd219c058432787f37b8ab9b056", "guid": "bfdfe7dc352907fc980b868725387e9845edfb7c28f28ebcc367c52b98eee3c5"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98539cb1f7f9b92d7f3c11fcac3fecd387", "guid": "bfdfe7dc352907fc980b868725387e98e424c2307fec2234f6c18c96a7830c34"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98b04877be44636d75d73f602b567b59ae", "guid": "bfdfe7dc352907fc980b868725387e98fdff8f1e95de8f7e216245e79ef4c9d5"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9886ff01035e35ad72c3fac41f2b43d91a", "guid": "bfdfe7dc352907fc980b868725387e98c6a18fdd9b10b58f2a9abad7be62309e"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d6cb961df93955a8fb21594140384667", "guid": "bfdfe7dc352907fc980b868725387e98c824d3db72acd30549d5bdc93000225a"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e981ab2abd3b7c2f6ffef97d060004b11bb", "guid": "bfdfe7dc352907fc980b868725387e9846c1878b3b437bb32ae2ba2716888885"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988ee7d4245d68ea63dbd4bb46001346bf", "guid": "bfdfe7dc352907fc980b868725387e984cba01cdd100b2568f140ba12a270b96"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d863ca241804f0a84b0366775b0ac206", "guid": "bfdfe7dc352907fc980b868725387e98c48d341b5bdac95a450767c904defc40"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e981fb9185e8f06f73143c7b87d9ebd3bae", "guid": "bfdfe7dc352907fc980b868725387e98963359516171aaba6d2077a5aa85c82b"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9896a1e797f25bb242b60ea7fdf0b81a27", "guid": "bfdfe7dc352907fc980b868725387e98b6f7452214b825c0a4339e38728bbb14"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e988d18cb57144c56aa53e22dca64295091", "guid": "bfdfe7dc352907fc980b868725387e98829e0fbd12cb388b7c48d2717c2b6332"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98cbbfdca64af95d0fe8d8afe1b5066eab", "guid": "bfdfe7dc352907fc980b868725387e98238f856f379240afda80a948f8ab6db7"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98197c297022c8ab6e05074b1d9276b870", "guid": "bfdfe7dc352907fc980b868725387e986afe5831373e365d8c6b9447d0711d96"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e989832721f711c0ceae2542dc057500a8f", "guid": "bfdfe7dc352907fc980b868725387e98488f64bfe4f80345c23b8efd59d24edf"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e980eec6e85afb523cb79810ffce3a75b59", "guid": "bfdfe7dc352907fc980b868725387e983bfb03502a41b4e3528f89829d391ef2"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9817c19a651fd6759570d16b11e38d7c00", "guid": "bfdfe7dc352907fc980b868725387e98cab75d4b9c361827c358fafc87a77ecc"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e986a8e859511170caa446ce01a2a803e96", "guid": "bfdfe7dc352907fc980b868725387e988c2dc8edc5ae027935000bb15dd6e988"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98dbcd82938ea905148b37e85261cb54a2", "guid": "bfdfe7dc352907fc980b868725387e98e702c2447754361a26d3fd33d3723171"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98dbb0f4b770e7b38d9a918647f246515c", "guid": "bfdfe7dc352907fc980b868725387e9813995320bfecd75892a8c01a1fff0e3e"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98683ad84d645d0099a7e417aebe9a1299", "guid": "bfdfe7dc352907fc980b868725387e987cddb1034d08c7790b1a53ec463f00ed"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9884566169b418042054bff6330e6488bc", "guid": "bfdfe7dc352907fc980b868725387e9860af6a327d81f4b1f24b79b5244bc50c"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9827597082252d1c574a722944f97f97f4", "guid": "bfdfe7dc352907fc980b868725387e98d6032d9098c28e5bc350369de23b9762"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9886d07e28d9772c86e6acf17144b75148", "guid": "bfdfe7dc352907fc980b868725387e988b7e681a58b08c0d10a0381cf1746508"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef493911d0782620ba0c1d4896beac8a", "guid": "bfdfe7dc352907fc980b868725387e98eeac5c566ad0ae4a14a20df9906b8cb5"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d33d5b354f6b4e6dfb4836b61b24611e", "guid": "bfdfe7dc352907fc980b868725387e9846e9df701e839eb8fd25f90f60928b47"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98c294ae6d637cc22d6b495e79b9b74c30", "guid": "bfdfe7dc352907fc980b868725387e981c7ad15f8c990434c51e40d29ddfe82a"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98eec29c99ab0c1e329b9d18fd1d23faa2", "guid": "bfdfe7dc352907fc980b868725387e98bc7c34d2dd632e9ba0ec13b62963a7c4"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e985dfbfbdd404e46a0a1d3dc2c8a44c576", "guid": "bfdfe7dc352907fc980b868725387e985bd057a4effb3750be057bfafc3213ae"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98bdb5850595167106842095f1e14ac8dc", "guid": "bfdfe7dc352907fc980b868725387e98b7dfa70c1fc4a23511f6722a10c3c91c"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9860b6d0ec493ba4f37bc302b2feb0d173", "guid": "bfdfe7dc352907fc980b868725387e9817173c2f153b0e32832f47946f4e8755"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e982f469d722927789cc269acd852d7f7b8", "guid": "bfdfe7dc352907fc980b868725387e9865c191e7db774fafe72b34434878cdb4"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98bbe0c05b6914ae56ca839f7a59fbcaa4", "guid": "bfdfe7dc352907fc980b868725387e982bfda72446e4a19702c6c12098a5ec01"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98d35cd3d90b959d636a154287d2a34bf6", "guid": "bfdfe7dc352907fc980b868725387e9840e659f36418ae02be49c899257593a3"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9846d207e8ae38f00322fec80986614271", "guid": "bfdfe7dc352907fc980b868725387e98caf692c6b5bc862386e814ad95f7c3a0"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e9812b98f575b773a9437d095ea83265d47", "guid": "bfdfe7dc352907fc980b868725387e9846fbfe0c7ba45df071662d45cdbf0454"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98e407f0b6c47b596d1e1113bbf91b4bc2", "guid": "bfdfe7dc352907fc980b868725387e984538003937c34986fd0122cec61be493"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e983c004299cf266b0f7786683b3ede1326", "guid": "bfdfe7dc352907fc980b868725387e989d483fcde203e7d962da2c10690d9ce5"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98ab07e7a67da1067d3613a08d50f36de0", "guid": "bfdfe7dc352907fc980b868725387e980bd3135a0829787ab141da19f93f5445"}, {"additionalCompilerOptions": "-fno-objc-arc", "fileReference": "bfdfe7dc352907fc980b868725387e98f80d06c64a61e217fd2cf5a28aba2c7f", "guid": "bfdfe7dc352907fc980b868725387e989704edaa67991b1f30c287c8b521e65d"}], "guid": "bfdfe7dc352907fc980b868725387e9841113a070769c2dac510eb62f9c6b92f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98d443d6935cad627567415f9246e4ff6c"}], "guid": "bfdfe7dc352907fc980b868725387e98346030ef90b086e87412750f7682c638", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e985a4e155e8aea42ef03a0b16a4535eb08", "targetReference": "bfdfe7dc352907fc980b868725387e984fe1f454389a944b317683cfdba2e41e"}], "guid": "bfdfe7dc352907fc980b868725387e9857923808ca5f338338ed382ea2f00eb9", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e984fe1f454389a944b317683cfdba2e41e", "name": "leveldb-library-leveldb_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98812cb744006e837a06a1ec7fcf8ca389", "name": "leveldb-library", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f420fccb1e56f8b9e42b1b3b5ef7a8c9", "name": "leveldb.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}