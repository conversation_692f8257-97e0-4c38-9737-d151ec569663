{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986784d7d51fae1f289d1be4f2ac7523da", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ddfa3b43073b5d1807637eca7a4aa451", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985d3215328aa3b62da03239a47844506f", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984afaab2570c48e968feba34e7d482159", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e985d3215328aa3b62da03239a47844506f", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9888e24bfb9535064ff915cc4bccc59aab", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98385de55f743b33ebb4f3359828e32a89", "guid": "bfdfe7dc352907fc980b868725387e98bd5d3b602ae59c901f9082c1f71c863e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8286fa02d22b0791a5caca141ce452e", "guid": "bfdfe7dc352907fc980b868725387e984a183e8270708e25d44d95991a2c9a40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b6fa43b1a277d5a0b5c4368130d9cbc", "guid": "bfdfe7dc352907fc980b868725387e98cefee417b76567a176f95aa4fc14e5ab", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859dcbc0f6ca62524937a9d24b3037f20", "guid": "bfdfe7dc352907fc980b868725387e98268adfe6ed9336e4a65c2ef0d5f7ca2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fd66697d617b723ee32403477fd58a7", "guid": "bfdfe7dc352907fc980b868725387e98b4d354bd742589846604854289ca2f79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810c6e56415a496f3f853eecf4997c39f", "guid": "bfdfe7dc352907fc980b868725387e98f00c6d998ab34d76756376462b753ad0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980fb306fb2560032133e2092ea46f7b94", "guid": "bfdfe7dc352907fc980b868725387e98992c16f3180d9475193ee3179e03b1c9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b3e8401fee0787cc43adf2970e27cca", "guid": "bfdfe7dc352907fc980b868725387e98b9cd2d6fe610316c24f585114d7fff3c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c20298322c39cd5205e9c9f741d2fb2e", "guid": "bfdfe7dc352907fc980b868725387e98b70ec31f34f1b65bac71ed6f2de06362", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98889a1b85b8f0bae5822039350be8a98c", "guid": "bfdfe7dc352907fc980b868725387e98b4333853e5ba3d5879b2dde4efadcc57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870ab290c5ac3b539effd046b8464d36b", "guid": "bfdfe7dc352907fc980b868725387e98a8da49999764316e1544645095199979", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb3a5122bb14f3d49a322cb026c7d6e0", "guid": "bfdfe7dc352907fc980b868725387e9899a5247135d79aa1a0aeb4048e10758c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ea35e3e7c0221a853c8bb5b86cc2ca6", "guid": "bfdfe7dc352907fc980b868725387e98895b18f0c934a96dae8d265065b43102", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834f41c06fc1b9677b14c5d2c7f5d33e2", "guid": "bfdfe7dc352907fc980b868725387e98a2896ed3854e32568a38da3b86a7d121", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf63e99f4ed5956aafc094948c3a9db9", "guid": "bfdfe7dc352907fc980b868725387e9824b3504f8da49cba56436cc50a083a82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3ec32d694c270cd537c83b9445a7165", "guid": "bfdfe7dc352907fc980b868725387e987fe42a313b1ccfae111d752f85ebada9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db743ce8f84c9177935e14f51ae3fe2a", "guid": "bfdfe7dc352907fc980b868725387e988a1d627d30723d18475169cb586d9648", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981844bc74cde6c4ae11f0d6abd07e9401", "guid": "bfdfe7dc352907fc980b868725387e98d03a9bfc8cb5795d632e3bcb6e58b51c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ee64bc72cca40360c7adddcfe857361", "guid": "bfdfe7dc352907fc980b868725387e9803ece5c7774e90d3e7db38ff24c20012", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cedc36fab4e4ba5bfdc5585bc43790b", "guid": "bfdfe7dc352907fc980b868725387e9871b050c9fb6de10fc1920ed5161cf7dc", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c78648077e5afa1ec551693d62fd7786", "guid": "bfdfe7dc352907fc980b868725387e98e033e86fd704f85c2a829dafd1dc8e39", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dfa1d838d87a4e6b962d9c1bd5ae4e4", "guid": "bfdfe7dc352907fc980b868725387e98a86f126fe928f2329428c8bfb47f19de", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989b614ca4ccd3740b24b7ada5dcd13f96", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9832ae7822dfc87f380f33b2b3a834e6da", "guid": "bfdfe7dc352907fc980b868725387e98aad481f7b825cb7fc29090c741aea1df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fc3182d288cbc535d1ce4bc81ca6d74", "guid": "bfdfe7dc352907fc980b868725387e982721ebfb01d69f751fb2c284d025c50a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c643d679ccd27502971a3dfaff46692", "guid": "bfdfe7dc352907fc980b868725387e98bdc0309c1d82773f0ad502e434739561"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e172f6576f165550e31f6f127f0a1fc", "guid": "bfdfe7dc352907fc980b868725387e985a630732171e7631adcd3d283f71d106"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d64b64938db9dc80339d7ddfc5e947a8", "guid": "bfdfe7dc352907fc980b868725387e9846cea13d90f8109c3c4cd36e95bde1af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd53ff2017372bacd7b04d8d48bcc474", "guid": "bfdfe7dc352907fc980b868725387e983efccfdf8315c3dde720d76afec6ced5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865bbb203040ffc38388772070c060a8b", "guid": "bfdfe7dc352907fc980b868725387e98ea75186a8a5c0db4fc2bb54d7591d3b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867f4b766078499cf3f42b91f662a9308", "guid": "bfdfe7dc352907fc980b868725387e981c816dc858ce23e8e7dd7794f03628ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2012989878880392e9786328fd940f0", "guid": "bfdfe7dc352907fc980b868725387e985bab31cf531fd2df95bc0e56b781bf84"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f8c15f08f38c4ab7b82f245f3eb3695", "guid": "bfdfe7dc352907fc980b868725387e9852a9be7f5c44b4928f7158f38e38deaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e441a76eac571ef54e5f030825b33ad", "guid": "bfdfe7dc352907fc980b868725387e985ba09c3f83d4014912a46e8036aa4c3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d6373e08ca9db7cfa3fb82535a77c3b", "guid": "bfdfe7dc352907fc980b868725387e98e9d919bbe7f2f5b103a9296fac92ccdc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f420282471d3ed4244a9b3b62f97ca4", "guid": "bfdfe7dc352907fc980b868725387e9893d63b5fa3afd57538b150e3c922c921"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d3056781a020175818d78d6a2cbf925", "guid": "bfdfe7dc352907fc980b868725387e98ca67756199b087ca737f541d950fdce2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864daa12c6ffb48c3b1aa6ee1f2b2c8cd", "guid": "bfdfe7dc352907fc980b868725387e981b978219d68adb770b9d8d4654e0e095"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcf1f3f660585f3b3ae929bce8cfcf8d", "guid": "bfdfe7dc352907fc980b868725387e9882b13820f48163e1407e9647ba2442b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dae5a6c162c0472d3e725a4039566873", "guid": "bfdfe7dc352907fc980b868725387e98242c2d63fed10b6549aeab7fee59601d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98645394e460ccf24defbaeece21f3ee71", "guid": "bfdfe7dc352907fc980b868725387e9873f28b6cbf36206b82065302bcc9506c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859eee2ed8ab7464f83f8ac6cb62e0837", "guid": "bfdfe7dc352907fc980b868725387e98711a8f490a79ff95ef73f9f59d9deecf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ff436fd2dbe57942e737548830d360d", "guid": "bfdfe7dc352907fc980b868725387e98f82538251cbb63e37eac0a2c01db6ed6"}], "guid": "bfdfe7dc352907fc980b868725387e98b7cd942b352926a13cd0219d60ad1f74", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e98bf2f258e7a5d5954814d5a550605b591"}], "guid": "bfdfe7dc352907fc980b868725387e986d34a337e75eb79f44ea376c853dc40d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c59456f6720a2d1c19a68caaae9be1ae", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9868e161d94cc8a768ad74d2dc41077621", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}