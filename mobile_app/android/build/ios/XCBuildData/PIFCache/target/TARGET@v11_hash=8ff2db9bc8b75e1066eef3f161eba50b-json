{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fcfdd25071f2b44049768d79bf63e742", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f0d1e5eac255304098d0ac357f4cb6ee", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a9992e8e8b0b5abc6e5fa25a0cedbc04", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98bf5e64413e3f77772699e0cfb1591125", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a9992e8e8b0b5abc6e5fa25a0cedbc04", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98430998684e8da4535d718a33a1d34296", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b361b0707737e74eae818d1c0c4114ed", "guid": "bfdfe7dc352907fc980b868725387e98b5a56635091d5ad20d504a2058749806", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895ed76d132b8f35ffec437d897126f90", "guid": "bfdfe7dc352907fc980b868725387e98b8447e18e065039a494ddd6ddfa503fc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811d207a89b312fd8f5f52f8fe305e469", "guid": "bfdfe7dc352907fc980b868725387e984b6e148230dfef42d58b62655c1443b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f01ce2fd169c4420528a20e3d37d456b", "guid": "bfdfe7dc352907fc980b868725387e985cf6bb42143da6a9fd84ec252c07d09c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eff4f71cda14d4cc30f94b2d923552c", "guid": "bfdfe7dc352907fc980b868725387e9863a9d9ed4f140c805e37d1d20c0c9127", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5d08a99bf38111babf4a31ab71afb5c", "guid": "bfdfe7dc352907fc980b868725387e9833009f84cb1321b3fb58b0e8703e5812", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dda94e63fd44289fd78ec4c885ba037c", "guid": "bfdfe7dc352907fc980b868725387e98e018c78db9ccbd1ff83346381bc8837a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897cf16ed7f6573470efc7ba8406c6422", "guid": "bfdfe7dc352907fc980b868725387e9812759169f65179de8394ca913be0ae2b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d41a971da209c1c439f941e2ebda7ee", "guid": "bfdfe7dc352907fc980b868725387e98c48bd8e8fcfcd7da9e642306637adb9f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f50788c38bc74975107c3db27a7f48f", "guid": "bfdfe7dc352907fc980b868725387e98e49212231d91eae9cafa8dab13d2b8d3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800cd6acca1121037621c72ebe35463c6", "guid": "bfdfe7dc352907fc980b868725387e98973c2138841fb370aafbfbff3210f96e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e89906ae9aa44f3d6a15cf1ed22ce4d", "guid": "bfdfe7dc352907fc980b868725387e98f6c9e8bcad609395f91ec7512c7d4719"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896cc68e87598bfd1f655198199598476", "guid": "bfdfe7dc352907fc980b868725387e98dbe52f6fc156c4c4c7863e6e54d1fcad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cba810e9ba7e0b24057a2978bd27f346", "guid": "bfdfe7dc352907fc980b868725387e987c652a8e6f014dc4fb8d5f4b8c8445c4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf17191c85951f713711bd47ee3ed048", "guid": "bfdfe7dc352907fc980b868725387e9800368a22b325a3d87a9d2042cca9046d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6ca931256540a13b8e3761158117339", "guid": "bfdfe7dc352907fc980b868725387e98ca4ca7b9ed175d0850d2641b51f9c972", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883194dbd5089217ea6a883f4f13a70ae", "guid": "bfdfe7dc352907fc980b868725387e981e8dd8fe61521469d41396af366a3b3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7b0cccd229774bfc4da562fb33beb91", "guid": "bfdfe7dc352907fc980b868725387e98e6ac68da7c68c26bd60c4f1f7cd066de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d577c9af184c0ee190d59088f728636", "guid": "bfdfe7dc352907fc980b868725387e98a07319ff295231f1a5c1fd59f7b0ea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c62bc1ae97451d418461ce5ff659d553", "guid": "bfdfe7dc352907fc980b868725387e98a46eb4508eac5a45cdfcbb93645662e9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c1530f22241cc4a4b4450ebbb6cfb68", "guid": "bfdfe7dc352907fc980b868725387e98ad479908303e528a8c9ea6545abf409a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2a3c1523fce003f98bf7d085c57fbff", "guid": "bfdfe7dc352907fc980b868725387e9803d50f332f1d660cf4ba9d213920c5d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981845bb81b4fea59d98db2c315d88e0e9", "guid": "bfdfe7dc352907fc980b868725387e9896c85b8d38fdf380bd4c60f3a3ad5b38", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988916dd8f2d6d0c6a78cea72894ab665f", "guid": "bfdfe7dc352907fc980b868725387e98703a87856c189e56bb8f71570d333aa6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e9b16a98355f0ab57e70de5f696190a", "guid": "bfdfe7dc352907fc980b868725387e98e3bdc93324e66b130829014f872e276d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982939ec8d8a2ff484c66f93d2ab84389f", "guid": "bfdfe7dc352907fc980b868725387e985330e1e33c7ccffc3503be70cb366860", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d6d0aa1a34156c72f8464d730266cb99", "guid": "bfdfe7dc352907fc980b868725387e986b80c86dfdaa379087bdcec3aad08858"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98413cf7a23333b21245ab3dfb125f7e56", "guid": "bfdfe7dc352907fc980b868725387e98ee265994029dae1e3ad57ecd5dd87a65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818998bd9e448f7ce254ab48b8ed10ee8", "guid": "bfdfe7dc352907fc980b868725387e9875ffb76a88dbbc4a01e1ff71feeb5095", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e594be075be93ecd10710bf19855470", "guid": "bfdfe7dc352907fc980b868725387e9884b9650daf9a8d442e2c94a6bc10773c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9880122e83721b1456db79f6c75e40ea6f", "guid": "bfdfe7dc352907fc980b868725387e98b9a9dc6cc45612943a05f74f9081a716", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98c08dd816f39ba9f6a1cf7e9b14084b27", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982a2bc5c85c3c9198e2cca391ff9d013c", "guid": "bfdfe7dc352907fc980b868725387e982cbb96688ae13662ba9131571b0fdb6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe52ecc61962f64b80f4b509e6ac25a6", "guid": "bfdfe7dc352907fc980b868725387e9847bb8780dd9b8f33bdce494ef90fac4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98233c62a2e357310b13bf2a7159243aa0", "guid": "bfdfe7dc352907fc980b868725387e98c7787ac1ed5ee8983ddee019ee616c81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1a74e5b00f1206a002ecd7c29183e44", "guid": "bfdfe7dc352907fc980b868725387e981128d828ff893a663fe7ab48b3114fae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800c2823bc60fa85fd4332b8fcb5ccab7", "guid": "bfdfe7dc352907fc980b868725387e986d9542371f45e39d8c6dfc002de12a2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898c4b35693ba19a2d27ee53dab74aa9a", "guid": "bfdfe7dc352907fc980b868725387e985f6171eed55788fe21247145c648f032"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98499719b00a459b3fe9aa96499a82b2da", "guid": "bfdfe7dc352907fc980b868725387e9803071bea67be3d2bdeec8f9eccff2b66"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806a4f94dc4adc1199b417c06c065d221", "guid": "bfdfe7dc352907fc980b868725387e989a48340503be8c3f700b77e0f946f0fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a67c7964e93b62e6f259e76e12027062", "guid": "bfdfe7dc352907fc980b868725387e9823e56c2173c4d4745facbf5bbfb8cdb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5c77e21aab8c11b65305c9d3fea6170", "guid": "bfdfe7dc352907fc980b868725387e9886bff70b97f041274dddcaee311f136b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c04b9f03b18c4238a8c078205e5beec", "guid": "bfdfe7dc352907fc980b868725387e981777903b058d2d043f8b4f1c4a42c3a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98399b3d87264be08be2aa5c659b548b3f", "guid": "bfdfe7dc352907fc980b868725387e98360d4e6c8af04886e03002da04eb9df6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a2e979d8b9fd2a108ccfdab2535d43d0", "guid": "bfdfe7dc352907fc980b868725387e984235e95cce92c85ac565b92d1e548195"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a957b39039217298798fea7281b6235b", "guid": "bfdfe7dc352907fc980b868725387e986487871139d01d1d8b75737654df4471"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ee5b3ebf2094fc78d91974176f17735", "guid": "bfdfe7dc352907fc980b868725387e988d0ed397d4c700dda2777032e0d337aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839f8ce294bdf43ba0de713e5e9e45625", "guid": "bfdfe7dc352907fc980b868725387e989129b6028efbc55285f793e0d52cf280"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a061e3c69c699addac716134f739a945", "guid": "bfdfe7dc352907fc980b868725387e98fe6ba47914ed88f14aa22090fd2e2835"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ef631a86fc080df92a1162f559fb09cd", "guid": "bfdfe7dc352907fc980b868725387e987f052480f19fa21a20bcffc02be2b9b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d737cb79fa9aa1b554d452e2193f3dae", "guid": "bfdfe7dc352907fc980b868725387e98bc4ace4c46d75b9a7bfcd3228bf3563e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876e4f5b5a1db8f7127e062d0e666848f", "guid": "bfdfe7dc352907fc980b868725387e980605c9a73354a10bed22489b6a503d62"}], "guid": "bfdfe7dc352907fc980b868725387e9818461962817714e6813c6f1feced57bd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e9842285bd1d89c4beda9c9100c9e01c2f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b2d6d2a2281a64563abbb1eabdd5a3", "guid": "bfdfe7dc352907fc980b868725387e988a1b6f7d07a6d02269bf3a1e7f6c5bb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a8de0ab70f2c0fbe5d9ba1a1011119", "guid": "bfdfe7dc352907fc980b868725387e980f2f92e8d0fef744338217e07a70f92c"}], "guid": "bfdfe7dc352907fc980b868725387e9839b2bbdd0e112b8e275e90b99343c840", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d46152f675049978f617afe3766d652a", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e989ffa45af09d175ea388642d6ba5df6d3", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}