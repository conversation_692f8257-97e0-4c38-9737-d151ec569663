{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98752f2d431eaf6c337a8b07e99aeee88d", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS": "arm64", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982bfd95f1f48ca6b1348a66db18d21287", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ad8938acf8a850f82f5a621064507259", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5219d7330cf2e66b18607e2989676e4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ad8938acf8a850f82f5a621064507259", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e842831931c06a0076ce7ac97580821", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981dc3c7d40b7364588446fff39c89ca23", "guid": "bfdfe7dc352907fc980b868725387e982f7568700678161c9dd366d7739bf413", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981952fba7b7ad338678ba28fbe5a80230", "guid": "bfdfe7dc352907fc980b868725387e9897cc44bd81953971c4411b34ac43c020", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833e4095b4282b9c277fd0ccfe1606770", "guid": "bfdfe7dc352907fc980b868725387e98a0d221f6b774f1de72c66851817ae4f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8a61b5fb23cebc199330d36b96b6e48", "guid": "bfdfe7dc352907fc980b868725387e988855acd76c8609b96ea98b33b58b6c0b", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898af355f0a4e861fef47c1b5ada0ecca", "guid": "bfdfe7dc352907fc980b868725387e985b821809a5c1ff0442567c92d9929bb8", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885389778bc2df71b49ea08382e7a7ca4", "guid": "bfdfe7dc352907fc980b868725387e98f496b1323513651b3b4cbc81b0e20c64", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab84475a95442f29206434c19da98291", "guid": "bfdfe7dc352907fc980b868725387e981c6bd2aeb005eb5aa23d604cb5a9ef37", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837524da6c2abceee5e6e51372364375b", "guid": "bfdfe7dc352907fc980b868725387e98e1d7081478985de1f4b9f884b064d6ae", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98843f5dc0c61ec3de6c51a2beadf7a125", "guid": "bfdfe7dc352907fc980b868725387e98fc42f6a4da7d5f3fc81fb28705fdd0f1", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814cbe0c954eaa1f7b5dec2189198858a", "guid": "bfdfe7dc352907fc980b868725387e980d755e9f0a80e165d794c9591f3688ef", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbabda69f350e9f470c9b0f2b21b4981", "guid": "bfdfe7dc352907fc980b868725387e985d63336ec3c4d846ab3c00ce318b81a4", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890ea6be8fc2f64b192d03ca5391ee9c8", "guid": "bfdfe7dc352907fc980b868725387e982663d391ae85a2b80b70471829aff3bb", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b1decdc90e178ca35f4c8d81bf74148", "guid": "bfdfe7dc352907fc980b868725387e985eed4a068a35f1a0879fb20b8f16c795", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdf1736e6e3849deb0b50f2cf8757168", "guid": "bfdfe7dc352907fc980b868725387e98e00ce16816479a05d87f747bda04c6ff", "headerVisibility": "private"}], "guid": "bfdfe7dc352907fc980b868725387e98527db3c40639cc6759f0e8015001fafc", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9872530cab3d24963d6391b11ab8a4ecb8", "guid": "bfdfe7dc352907fc980b868725387e98475fc8e8b3ec4b7dd01534402dce2e25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a36dd189f4d320624436c04ea5fa805", "guid": "bfdfe7dc352907fc980b868725387e98edcd7d17c21e323d8f23936019f574c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f646eabc353b2b8ca3f805e2e26d288e", "guid": "bfdfe7dc352907fc980b868725387e9895433e59cde7b56df01e82dd15f972b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebdc7ece956e766634f574ca672463cb", "guid": "bfdfe7dc352907fc980b868725387e98a7e24caaf141d24016754a3fbfd270e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7f2af9aee60e6c76701f03601472c8e", "guid": "bfdfe7dc352907fc980b868725387e983b1edb6524ecb8d936212affa1e5e982"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98718ff14f42f1d682b8dda876a6c09aa0", "guid": "bfdfe7dc352907fc980b868725387e980048cb65c12726053d172af4c2cc57ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3ce2b06b2693a769edff70cea28a334", "guid": "bfdfe7dc352907fc980b868725387e983216d06242794229d522e54093962e62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987dd087bb75dc537a007bc912fa2f437e", "guid": "bfdfe7dc352907fc980b868725387e983c5f2941ccdd4c9c1519c5a1387be124"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988328caceb1a6ae6dc62e96340050580d", "guid": "bfdfe7dc352907fc980b868725387e9878647aa3288b26947c0ec7a3ee99f81e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830027e234171e0813ba080786a801dcb", "guid": "bfdfe7dc352907fc980b868725387e9850b8385449ca49600709db2527b53b96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aeee55e2abba02ec5af1cc1ea2ce2978", "guid": "bfdfe7dc352907fc980b868725387e98fe6f4fd9bc217be19ed1ad266c06cabe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dc6e78aed6e50256a5ee981853f116f", "guid": "bfdfe7dc352907fc980b868725387e981cb135143e325a23b8ab2d829dd02b39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d4ac3d5e73a7a52a9a35002e7c0c4d2", "guid": "bfdfe7dc352907fc980b868725387e98305d6615e7c6cb637b8d6fd5fa87f63a"}], "guid": "bfdfe7dc352907fc980b868725387e98445c13b134f6717c552fb845dd07cf6c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e987208fa6207d938fe537d0e8359bf5699"}], "guid": "bfdfe7dc352907fc980b868725387e98ee8cf58f2eaf1185d54859fdaf1a4231", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98e1b65cad46427b34676adbcb934284e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}