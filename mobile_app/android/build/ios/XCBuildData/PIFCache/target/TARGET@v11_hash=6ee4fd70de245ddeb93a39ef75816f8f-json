{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98398d79344fb21b1946b27731e7d4984a", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/fluttertoast", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "IBSC_MODULE": "fluttertoast", "INFOPLIST_FILE": "Target Support Files/fluttertoast/ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_NAME": "fluttertoast_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS": "arm64", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98c39e1bc9025121f7518820632f9c8f14", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d0446fa48fff872ea6a0fbb79d95956", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/fluttertoast", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "IBSC_MODULE": "fluttertoast", "INFOPLIST_FILE": "Target Support Files/fluttertoast/ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_NAME": "fluttertoast_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98f8b521254bdcc568912974c6eb08f1df", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d0446fa48fff872ea6a0fbb79d95956", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/fluttertoast", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "IBSC_MODULE": "fluttertoast", "INFOPLIST_FILE": "Target Support Files/fluttertoast/ResourceBundle-fluttertoast_privacy-fluttertoast-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_NAME": "fluttertoast_privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS": "arm64", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9815e443af4900d5d761514569662a10ab", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e981978f0ed04678bdcf054375bf57d825e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e982713eeaba63a502343186442a0ff9c33", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f512fc209ba1b910a1e362dc7589521f", "guid": "bfdfe7dc352907fc980b868725387e989348675cc1795efa89023540d933d6c8"}], "guid": "bfdfe7dc352907fc980b868725387e9840f1d145eaddd857706fc7365b4a8f0b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e985739272bce418ef50bd06c859612bad5", "name": "fluttertoast-fluttertoast_privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e989d4bb598ca0a92e1d0f3a4ef0157bf7e", "name": "fluttertoast_privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}