{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982cf0931f5f316519206391f2d461dbf9", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseCore", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "IBSC_MODULE": "FirebaseCore", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_NAME": "FirebaseCore_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS": "arm64", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9878a53bad0c809551205d187697f9e66b", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e44b7e8fa741544b8476c5c60b0567c2", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseCore", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "IBSC_MODULE": "FirebaseCore", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_NAME": "FirebaseCore_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e988217367f07fb4e284dd568be8fdc505e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e44b7e8fa741544b8476c5c60b0567c2", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/FirebaseCore", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "ENABLE_BITCODE": "NO", "EXPANDED_CODE_SIGN_IDENTITY": "-", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "IBSC_MODULE": "FirebaseCore", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/ResourceBundle-FirebaseCore_Privacy-FirebaseCore-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_NAME": "FirebaseCore_Privacy", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS": "arm64", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e984f27a9a721bc25eadc6f5b4f700888d3", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98a2b2454dfe98a5ec6f69424ade8b490c", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98cce946a460448eac781e47f43412cb04", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e91b0fd8501d679100444e50d9b2dbf3", "guid": "bfdfe7dc352907fc980b868725387e987815932c571e4c6394d1aa153f2a177f"}], "guid": "bfdfe7dc352907fc980b868725387e981dde6007f53bfda584f35fe8cb19b6c5", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981126092e527a43878ba047c0d6b5be37", "name": "FirebaseCore_Privacy.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}