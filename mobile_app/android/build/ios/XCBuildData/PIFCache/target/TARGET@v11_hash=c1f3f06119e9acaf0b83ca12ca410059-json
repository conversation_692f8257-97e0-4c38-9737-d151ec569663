{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983f5f0b8c4236c4349f34e8c9653f5e37", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS": "arm64", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988146734d5deca58e25c7e634361446dd", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988146734d5deca58e25c7e634361446dd", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981079d75895bdd10552b95bffb134ef6f", "guid": "bfdfe7dc352907fc980b868725387e98c6f747c7484b28f9140be4553d75d6fe", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b37273e240dd9dbd3506ce2e05d3562", "guid": "bfdfe7dc352907fc980b868725387e98bb4e1eb5807c516980e284009bdf64af", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bfe90dcd64528fe74925efc4cd2ca235", "guid": "bfdfe7dc352907fc980b868725387e98dd78b80078ba0778c09f1cf7b2b062b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c3718548e819601b7195a7f4946351d", "guid": "bfdfe7dc352907fc980b868725387e981ab1e393fd5a37eea66b65cedc57ad1e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988355ff98641ed9b064fd2a9974f787bb", "guid": "bfdfe7dc352907fc980b868725387e981ac089f547e0d45d984d5bcb50e7fee6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3fd8949f019cd1140dc188e29b8fd0d", "guid": "bfdfe7dc352907fc980b868725387e98110620d225b33fa33f4e6438d9a18209", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f249fbf065ae27de0f63086189248a7", "guid": "bfdfe7dc352907fc980b868725387e982d82631c34e75d57fa762e9dad5875c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f02ecd1eba44e91615a01c145d9e45a", "guid": "bfdfe7dc352907fc980b868725387e989819bd162f037f5c0762f3a238a39345", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccc84e26701c56a14040c00d168447be", "guid": "bfdfe7dc352907fc980b868725387e98745454b08d6008581c227a1fbb00f815", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98354f5b86b17d9bf8e57c56f834696542", "guid": "bfdfe7dc352907fc980b868725387e98636ff1a2f23843ea4c736bb1ba11456a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831a2210dfe68e4ed7c53dec934a09e1d", "guid": "bfdfe7dc352907fc980b868725387e9806bc1ab2c2151dd3ccd116108cc110ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986181c4fd003d1cd23346ad6b1c5e05d8", "guid": "bfdfe7dc352907fc980b868725387e9880aeffde592e868c03aec511368e34c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ef0900e082608c1ffa0a4b62b3123cc", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d8ad1ca8edc6b4e88c38d6ab088fe54", "guid": "bfdfe7dc352907fc980b868725387e982aa3fe70c3cb09bbc855a3f59978ed01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829cd71271ec6a4622fb551be87a40629", "guid": "bfdfe7dc352907fc980b868725387e988e5342ba97eb3fdac5ef6f5449e6e0bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0dd16f3d95a0a83cc2ed4c94bd1971d", "guid": "bfdfe7dc352907fc980b868725387e98a01937a9eaedaf0dc4ba642460a94a01", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d70f632e80ac956d8e2ec93fdf2ad51f", "guid": "bfdfe7dc352907fc980b868725387e98349206708adada999c490aa68fe92b02", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae143a240977f8d47f47485da79e3f84", "guid": "bfdfe7dc352907fc980b868725387e98be6da3cb2ba2ea9aa6ad784a2f194a3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986534a3fc4367e796716fc2ac451329c8", "guid": "bfdfe7dc352907fc980b868725387e98539119c8c4e8f19e5729ab283f1f9901", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d914dc83075157c607f88237b1db6bb", "guid": "bfdfe7dc352907fc980b868725387e989e137505b96b2e7b2a43c1c0a838f2cb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce122487c4cb6feb39abf39e3bb9e989", "guid": "bfdfe7dc352907fc980b868725387e9802a5f4526a19e8f3b1991b23f5aacf5c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c27f2a9557b7fa2960e10509c4456a5b", "guid": "bfdfe7dc352907fc980b868725387e98ca8f046ab95da87baa9b5f4f5fbdea4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e7fd1faaf6504afbd7587bc4fb40026", "guid": "bfdfe7dc352907fc980b868725387e98ae6c767be8fa4216f0be91c97f5bbaa7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a91987f23cdb4643fff7370f1384bb04", "guid": "bfdfe7dc352907fc980b868725387e986668d9a663f439a3e0f5e4590bcfb72d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899e86fa714eff4e845d9b1f7466bb779", "guid": "bfdfe7dc352907fc980b868725387e980e7c0a90bc8c256d315aba4d4478a49f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ed6e78a65383b94069af12b6bcfc35a", "guid": "bfdfe7dc352907fc980b868725387e980b1b51f9ff2bc48f0df27ab6aa21f3c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98927df76d28485072c42bf49a7ef6b022", "guid": "bfdfe7dc352907fc980b868725387e9814e607aab5c381fe34f08e2a491727c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de2a35a4263994e0ce7ffd78cc906a30", "guid": "bfdfe7dc352907fc980b868725387e9835582aa8555f7da940e2d2c9c3c0cdc9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eca1f44bc302b016e3471b5056414e6", "guid": "bfdfe7dc352907fc980b868725387e98f7f976a80d3a542437b32ef7560ad1d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869280324b5ff3ac6013d318103d82aa7", "guid": "bfdfe7dc352907fc980b868725387e98223d665f4947310db6beda54bb499cba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb98311df8383c0cb0ff236b4c0e91d2", "guid": "bfdfe7dc352907fc980b868725387e984780fb6d7d82ee0f25d06179f037889c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98753e817cca795a68cd6488d55a1cb24f", "guid": "bfdfe7dc352907fc980b868725387e98f50d25efd6a7ebe1fafa2e7b461e196e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985017e007c2bbce64c71e10322844b19e", "guid": "bfdfe7dc352907fc980b868725387e989496deb33ce2c0b2847846578d84f467"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980559c7a1e6d52196884021f69abb068f", "guid": "bfdfe7dc352907fc980b868725387e98a8a1c5091bae5708b152dcd8bffc7b17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987adc29e4e573f62e13a36ef73a3f0cc3", "guid": "bfdfe7dc352907fc980b868725387e98b927261545bc1276a63f5d1491febbe3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9869340ba4901abf726d383bbbb7c4a5b9", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988519e0a7a044f837355afa9a1b5a0d7e", "guid": "bfdfe7dc352907fc980b868725387e988857b3e7a6d557be655e3aad9396ed0d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c24d3c0de57f36f0bfd1635159c3277", "guid": "bfdfe7dc352907fc980b868725387e980fc24c7e9f336288491c5283e1aa05dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984837af8aaa11284e4a0e1bc5d1e4a98b", "guid": "bfdfe7dc352907fc980b868725387e98b20aed7648bfd6c0d14eae1b7d855eaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f463d94f4fc3e3ab30910cdd6e75068", "guid": "bfdfe7dc352907fc980b868725387e98de033c3bf4d0d12515224032a6c330fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1347cee521b842f3875b074c55606e3", "guid": "bfdfe7dc352907fc980b868725387e98a237d0e3352a997bdb73dbeebd5e9ba0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8528221739bbad7a95280600417fc3c", "guid": "bfdfe7dc352907fc980b868725387e98a028228653657266f29ede18e8b863c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be2c77ecdf407afafa5c86d0ae4d7fec", "guid": "bfdfe7dc352907fc980b868725387e98e5682c805c4bfaae7eda4fba77751eae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e185dfb3ae7be54d12067ddb2562abdf", "guid": "bfdfe7dc352907fc980b868725387e9820c767839eedb700b8a68c8799da38fa"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}