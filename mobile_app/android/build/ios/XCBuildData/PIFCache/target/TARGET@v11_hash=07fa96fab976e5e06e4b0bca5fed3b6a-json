{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98f4d0ef364a11a53bb0e8ab1ce5e87134", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989defd51a073b6e0fe4a458ff5ead7b33", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851ca98ca8ea02651f745d3e1c8f8479e", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9808cbaa8ee817273bf89892f34d98d180", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9851ca98ca8ea02651f745d3e1c8f8479e", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d33a258f8724e3d5c1d4ea6297361cc3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98cd153156eec0ea44add41bf78ce702ba", "guid": "bfdfe7dc352907fc980b868725387e989a62fc8e59753c2f21bd90ec55b00f25", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da615c4a3871462bfa06075483a15372", "guid": "bfdfe7dc352907fc980b868725387e98964e81f1c62ba4f1b6c4d71c9f163265", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98442899482e353aebd804cca9f171e9e8", "guid": "bfdfe7dc352907fc980b868725387e98ff843ef6e169ac0fd0820a3c30c05534", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989578d7ba5af71d6656f031e7c9281f44", "guid": "bfdfe7dc352907fc980b868725387e98eb6102aa696b3b158b1bd9249d44a431", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f1b7cdc1d9958e2115729b9335da403", "guid": "bfdfe7dc352907fc980b868725387e98667c2932a3d357f8b8d8170498a242b6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c8453eaa3dbe35cc7922c28118d3795", "guid": "bfdfe7dc352907fc980b868725387e98f0373a09f1608347bd458266386daf15", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b20088f6a4f5ac8dd8778cdb4754f9a", "guid": "bfdfe7dc352907fc980b868725387e9825938f911c9a9fa5cec60122170733df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a612ae127ca256c60d08c90973ce1bba", "guid": "bfdfe7dc352907fc980b868725387e985c82f8c72ec45822ebf502c19fd80c22", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9881f5d4f18e8737df5740301c258081ac", "guid": "bfdfe7dc352907fc980b868725387e98e1131955bc121925519bde50ae1287ec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e9fe5fb38008c88e426ea40774622a0", "guid": "bfdfe7dc352907fc980b868725387e98c2ad0bd557fdc823afa2673ada5a79aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dd0ccdfde1921b9bc5443959386b94b", "guid": "bfdfe7dc352907fc980b868725387e989148f1e8e5ac942d9a37bebbb388850d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98142888a1c21e8e69a166269b12bf8a6b", "guid": "bfdfe7dc352907fc980b868725387e9839157119e809874c573135f83db51f81", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844140e0a857ec5b8c68422574166b2b3", "guid": "bfdfe7dc352907fc980b868725387e9824b0f3239fee5d413c42cb4b2e641781", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b196a19335eb7e17340146365709962", "guid": "bfdfe7dc352907fc980b868725387e98534f299969b75574d897eb2de73b13f5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989de27f9d87352d7db03bdce125ccdb15", "guid": "bfdfe7dc352907fc980b868725387e989bdffe3d43beb4d05c2270af3678a660", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cbb736ee58d4c95e439baba8d2e51a1a", "guid": "bfdfe7dc352907fc980b868725387e98b3fc909bd1509f8ee9475c4293369b89", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a87059198d7a9f9117a3545faecb01a", "guid": "bfdfe7dc352907fc980b868725387e98d28dcde7d19ebdbd5676e2e21eae54e6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98878911b8ef4f022f5cbf83ef86e19aaa", "guid": "bfdfe7dc352907fc980b868725387e98cc640b4ad11a79b411b95978fe4f295e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d79876fb4a0ef215d155aa74e599fde", "guid": "bfdfe7dc352907fc980b868725387e98c0b47c16294e15ad00b1711c2047b7ad", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cdf2f9469b5ca1ce4e1df5aafdb029c", "guid": "bfdfe7dc352907fc980b868725387e98f93d9a8df15d2fa5d9201fa1093b8245", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b85cdeec88e93e5b83ea8717e6367a3a", "guid": "bfdfe7dc352907fc980b868725387e9841d3a6a60365759c8d64a9e31489e66d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985495becea8f2fdca286a6b5aa77ac420", "guid": "bfdfe7dc352907fc980b868725387e985c5ecb538cb3665f3d7f3e58e3b3732d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da6703d6c32add52a41ae566830432c9", "guid": "bfdfe7dc352907fc980b868725387e98c6464fbee54430c86ebd43c8a95d6fb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a03c97793f24a8f087c1683744414fe0", "guid": "bfdfe7dc352907fc980b868725387e98c7135dc1ac247afb94fb990c2a7657a8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c45dd1a46f56614038d3cc0432f07a9", "guid": "bfdfe7dc352907fc980b868725387e9850fba2178a7f781a9c7d55f555d0b128", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9873e9a3e6ab65e4ef0e1df59c696afe55", "guid": "bfdfe7dc352907fc980b868725387e9885768ad0a9502c89003a06c800acdc1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98418c6d15c05daa968cfcf047ca1eb38e", "guid": "bfdfe7dc352907fc980b868725387e9890f2590b290f59eb52b6dcbbced8d8ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a4dd83a26b4352e447f756f8724fd7f", "guid": "bfdfe7dc352907fc980b868725387e98ff66b948672efeb8bf39783d25b0d9c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab8332437ce5670132fb0806d47b9031", "guid": "bfdfe7dc352907fc980b868725387e98907e5f68f031f28a487a40dec69dc5ea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ef4a88d59c021beb7bbf0225aa27d70", "guid": "bfdfe7dc352907fc980b868725387e98cfb7f74c5f62ce5136337464a6e9740c", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98b0a696a4297c70e0b0cc9bfe0af728d3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98446e1a77aa9af188d463481978f99f25", "guid": "bfdfe7dc352907fc980b868725387e98c8de085d71f3b7bf5f7f209cbef06757"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834c5a90db0660b647ec1f0214f1ebc20", "guid": "bfdfe7dc352907fc980b868725387e9895220c8ef12ad302dc556e506a6efb57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce3954da3f2d0d3dc0193da13c003081", "guid": "bfdfe7dc352907fc980b868725387e98e38722836fda7848d989190eb2893b20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a74b797aac4946040f512fca4b4b83ad", "guid": "bfdfe7dc352907fc980b868725387e9869d8f601b2dabea927c5098e2e11b6de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c4329368fe4b1a114c7d088bc7e20e1", "guid": "bfdfe7dc352907fc980b868725387e98371026b9c590715b5b34046ea07e89c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988779cfd274517b8cb5eda2fb53e7749e", "guid": "bfdfe7dc352907fc980b868725387e9804be3350d49581fc91cf699acd79a2ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9806c44ab203550e1866c3b3e77fb0c419", "guid": "bfdfe7dc352907fc980b868725387e98d6f706ca14c355f626abfceed6965d2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2bca25845d01e83c3c3b65e0230c19b", "guid": "bfdfe7dc352907fc980b868725387e98bd4712d0bf5a89fc8ec23acbc9c96bd1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989672852eed0f672df6875b90ec54a7c6", "guid": "bfdfe7dc352907fc980b868725387e98b5e8473676d18f8a919c24db50dbf809"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c029780d540437c5cc2ae0e538cb8c2", "guid": "bfdfe7dc352907fc980b868725387e9818322909be0788d9ca9c1d854707547f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e3111adbce29312d6ebd46ba4b7b449", "guid": "bfdfe7dc352907fc980b868725387e987a8bf1ab77471b39ad7252cb410295bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887df02b5ab593ae4eb4c1a659546b098", "guid": "bfdfe7dc352907fc980b868725387e982bb06d5119e59b1424a924690726e75e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823953500e715aafb3dc47f7cf6775741", "guid": "bfdfe7dc352907fc980b868725387e98259518a505a4e41009dc2110ab55cc16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868e83c1e04c7a8d2b67aadad6cfaedbc", "guid": "bfdfe7dc352907fc980b868725387e9861f7c4e9ab44a938f7d9523fe0c506f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98526a8168a5fd0fb30e6269af19d2b74b", "guid": "bfdfe7dc352907fc980b868725387e98f85e445173caf070762ca88dcd97e7b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df739fc9fc0dd17edf0b59d48775364c", "guid": "bfdfe7dc352907fc980b868725387e986e96c3f9173aa6c37ee932b9f76221d5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98647336bc9f5bb7984a6872b57443bc2b", "guid": "bfdfe7dc352907fc980b868725387e9862772a7a1841fcde3a1a71e085d62b1d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f30fc164d08bcdb071001d9dc9df055", "guid": "bfdfe7dc352907fc980b868725387e98814add23c2f4ec306f1effd41ab5271d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987693c7dd20b2a2fe20a37567fd98b9a1", "guid": "bfdfe7dc352907fc980b868725387e985c9e82a11a2bab89df5206e26c2a98b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fa64ea9d4cecc2be58cb7a6364f5540", "guid": "bfdfe7dc352907fc980b868725387e9800f0c62e4ebe8c05fc03a732a7840ee3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1eeaaed08f42eb34750ccad23e481ee", "guid": "bfdfe7dc352907fc980b868725387e98457a7f9b8edd047d0fcfabf3c3ba4b39"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816e00ff77cccfa6297ac16ad32d3184e", "guid": "bfdfe7dc352907fc980b868725387e98f601827b4843407d87c8aec10f66bddf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd1569109856b48ee3114dc25a4cee3d", "guid": "bfdfe7dc352907fc980b868725387e989f3db03a35f431b9a907923e4c4ab18a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9810e0557271e7b117929820df87bfdc42", "guid": "bfdfe7dc352907fc980b868725387e98ba47dfddefb71c4d8dee519a36c476a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa8726b82e8ad76cd6cc95969cffe1dd", "guid": "bfdfe7dc352907fc980b868725387e9884babe6c2892c38309defa6ff45a982c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edae47950604e49e332ee2828d1c173b", "guid": "bfdfe7dc352907fc980b868725387e989bd99638f51351b006414897d1263dc9"}], "guid": "bfdfe7dc352907fc980b868725387e98e7b7e6e4cb455111bed4fe98ae7e1ae9", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e982992a4429b91ac848803d742949eea82"}], "guid": "bfdfe7dc352907fc980b868725387e987e64315f3335f68010fd66b99c997c6e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c35e1c2b8f8fb95ab1c9a63a2d4726cc", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98ef6015f7a8cd8ec1fc3e11f1c5a32dd2", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}