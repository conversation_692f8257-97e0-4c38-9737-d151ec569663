{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982cf0931f5f316519206391f2d461dbf9", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9863f5325239f9676d2488910dc3d7de41", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e44b7e8fa741544b8476c5c60b0567c2", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cd94f741135242a42a09a729579881d9", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e44b7e8fa741544b8476c5c60b0567c2", "buildSettings": {"ARCHS": "$(ARCHS_STANDARD)", "CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES": "YES", "CLANG_ENABLE_CODE_COVERAGE": "NO", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_COMMA": "NO", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "NO", "CLANG_WARN_DOCUMENTATION_COMMENTS": "NO", "CLANG_WARN_OBJC_ROOT_CLASS": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CLANG_WARN_STRICT_PROTOTYPES": "NO", "CLANG_WARN_UNGUARDED_AVAILABILITY": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_GENERATE_DEBUGGING_SYMBOLS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_WARN_64_TO_32_BIT_CONVERSION": "NO", "GCC_WARN_ABOUT_RETURN_TYPE": "NO", "GCC_WARN_INHIBIT_ALL_WARNINGS": "YES", "GCC_WARN_UNDECLARED_SELECTOR": "NO", "GCC_WARN_UNINITIALIZED_AUTOS": "NO", "GCC_WARN_UNUSED_FUNCTION": "NO", "GCC_WARN_UNUSED_VARIABLE": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "16.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_CFLAGS": "", "OTHER_CPLUSPLUSFLAGS": "", "OTHER_LDFLAGS": "", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.3", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS": "arm64", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98eb1e357c59903fed0bf5d6f80c89bc88", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98bab5d78c31c422c28bdb63135a076686", "guid": "bfdfe7dc352907fc980b868725387e98f58b86b49f4561c9319b7392167753e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98860aaeca396278c545aaa88dc00cad92", "guid": "bfdfe7dc352907fc980b868725387e980587f2cc591363dfcbbdf0b6f0a6bb11", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981861b7c9d0d163089b283b9e844aa9a0", "guid": "bfdfe7dc352907fc980b868725387e987fb6ed2b939ae6221cf196b59636f169"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d91fcf69812333057ed3dbf4def09fec", "guid": "bfdfe7dc352907fc980b868725387e98a6950a6798a9bdaf791d1cdb45f1c4a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcc101bfa977392a947739d3dfee9df6", "guid": "bfdfe7dc352907fc980b868725387e9839e3807f0c05b69a3429bda92c3538da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae6510d571168b53b781574fc33ef903", "guid": "bfdfe7dc352907fc980b868725387e98264d1eedd49265cd6ded8bb831bc9a55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981492e03d6c37bc049c4bf4b6561a77ed", "guid": "bfdfe7dc352907fc980b868725387e9828b2cd86bb4c9759b0f65d622d621237"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9e43d7b72d151ddf6660b33591ac39d", "guid": "bfdfe7dc352907fc980b868725387e9828c60f66a8f7f0b171f9f33246002303"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a65cf45e4036559450eca36e2d045833", "guid": "bfdfe7dc352907fc980b868725387e9812573fc91ccfeceb40f78cb12e532e33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb4c198f4ed941cf7583e925704ac877", "guid": "bfdfe7dc352907fc980b868725387e983ee47f262b47916b46727206cd662482"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98880fb60b42af406ff0a674358ba44cae", "guid": "bfdfe7dc352907fc980b868725387e9869c58bcc2cef1baf28edb573dba237b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985249631301727fd9805b7e8bb41f573e", "guid": "bfdfe7dc352907fc980b868725387e98a8e11111a3456e192ea09222fee258ca", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b9d22d49e57d28cdd2a34cc95c79401", "guid": "bfdfe7dc352907fc980b868725387e98919ba6ec1c1a30a16adca5e9381a9ff6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf4cbf042bc8f8a0743093be4aed698a", "guid": "bfdfe7dc352907fc980b868725387e98ef78e869c877a2df0024a4ed41e3d4c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98789540b947c86c579491044d79436135", "guid": "bfdfe7dc352907fc980b868725387e981b7ef9c29181c4fe3340d055d329b7de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c2208a9013e2035cccbd0c0bd83bc6b", "guid": "bfdfe7dc352907fc980b868725387e98499892280ef7648258f4315a88749224"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98188b8c620487f6f3be4993a14767f13a", "guid": "bfdfe7dc352907fc980b868725387e986fd10a6abd4d769c124006e215d6d459"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c54c3eef9eccb8ad2beb8bc6405ab49e", "guid": "bfdfe7dc352907fc980b868725387e9824c19b77128fe82088f3d2487c3b0418"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffd208a8978612d2d64605d084857a2d", "guid": "bfdfe7dc352907fc980b868725387e98c4c60e9b1ed828dc26e500d0ee62ff1c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9815d584381f5fe6070bd4bb59a47d4d8b", "guid": "bfdfe7dc352907fc980b868725387e98fa7b4f88642578473961eccf3e95f13d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989f279bb9dfb9fc5f0bb61b04f24f025b", "guid": "bfdfe7dc352907fc980b868725387e98ac0824dfd40435eb9d31d6bd2595e396"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986ea68fc0e98be68f835f498213798fab", "guid": "bfdfe7dc352907fc980b868725387e98cb6467bb0fade97ef03fa3b94ec695fc", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989b6126e54ad783fbe6b4e3985d47160c", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985bdb7358a0d77d4ad6a6cb23a3342e01", "guid": "bfdfe7dc352907fc980b868725387e989f544b9f9abecc0593c8fdffa56e5313"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b319b938e2b3ac134690c7add24282", "guid": "bfdfe7dc352907fc980b868725387e981c7c63b9afdbc83120ed9a9ae527bcb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98734b7e83c56a5d89ab8ba3f878176bc5", "guid": "bfdfe7dc352907fc980b868725387e982d20a60176b21b59b33bc76f07104df3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5d4c2a186650151fd205ea7d66bb66f", "guid": "bfdfe7dc352907fc980b868725387e98de22f86655f82567bcbd3ee5b6370bb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bfc5aa7698b78189c11d564d31f985d", "guid": "bfdfe7dc352907fc980b868725387e98171aeaf98b5b78625decea736cfce073"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987674edb718a2329fc9be8e061f9ff23b", "guid": "bfdfe7dc352907fc980b868725387e9825b9dedcf5aac61bfe53b7b2134339d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98014f63e9a8c0ad8c51a1e6fa9d3de39a", "guid": "bfdfe7dc352907fc980b868725387e9827146908e86112bf5e7296bf6aad35b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b761ea0737046027e8a995a48d72ec27", "guid": "bfdfe7dc352907fc980b868725387e9849a7cb2edf8a162ddd25100cc0c415b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893f8cbbb959626a326687af0eecec86b", "guid": "bfdfe7dc352907fc980b868725387e9865da28cb35613221fc605f54f4107871"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c6e84876585531adfc5947587ed42b6", "guid": "bfdfe7dc352907fc980b868725387e98352417721854adda8c821c2f855afa05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5d7a65b40e1981cae71dd02966c7640", "guid": "bfdfe7dc352907fc980b868725387e989b2353ccc7b1e4d7d3c6efe674b071d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98909bfb978c0994b2a69b9c97f9564191", "guid": "bfdfe7dc352907fc980b868725387e98f246d238c0d2d4c3a40e879b09d62e1e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985decbcb54c170e9cfec6aa9d2fe95b78", "guid": "bfdfe7dc352907fc980b868725387e9899ad56e518323e9a9fba12b6094e392c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981648c59b42908177fc1d9de8de81b1a3", "guid": "bfdfe7dc352907fc980b868725387e981c55be19fdc085166c982aca08048def"}], "guid": "bfdfe7dc352907fc980b868725387e98590ef8cd097e8eb3cd7543233b84c957", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c9412876f0a5a6fbe054bcac7b5e7445", "guid": "bfdfe7dc352907fc980b868725387e988f7e85a8a0f75e7fc4b769255d5342e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e51514501e0f0d62f7ef6096d26bdc0", "guid": "bfdfe7dc352907fc980b868725387e9859c6b8e6485cccaba95f4bad03ec83d1"}], "guid": "bfdfe7dc352907fc980b868725387e98fd92a5a59bfa6d3b5dc0ecd438339816", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fa25d522291a397d9f0de1edad46f4d1", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98786312dc3fe129a3e12480551b02a139", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}