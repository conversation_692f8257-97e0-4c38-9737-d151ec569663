import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_theme.dart';
import '../../../core/providers/exam_provider.dart';
import '../../../core/models/exam_model.dart';
import '../../../core/models/banner_model.dart';
import '../../../core/router/app_router.dart';
import '../../../shared/widgets/elevated_app_bar.dart';
import '../../../shared/widgets/promotional_banner.dart';
import '../../../core/providers/banner_provider.dart';
import '../../../core/providers/live_test_provider.dart';
import '../../../core/providers/trending_exam_provider.dart';
import '../../../core/providers/quiz_attempt_provider.dart';
import '../../../core/models/quiz_attempt_model.dart';
import '../../../core/models/live_test_model.dart';
import '../widgets/analytics_dashboard_widget.dart';

/// Home screen - main dashboard
class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> {
  int _currentBannerIndex = 0;
  Timer? _bannerTimer;

  @override
  void dispose() {
    _bannerTimer?.cancel();
    super.dispose();
  }

  void _startBannerTimer(int bannerCount) {
    _bannerTimer?.cancel();
    if (bannerCount > 1) {
      _bannerTimer = Timer.periodic(const Duration(seconds: 20), (timer) {
        if (mounted) {
          setState(() {
            _currentBannerIndex = (_currentBannerIndex + 1) % bannerCount;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: ElevatedAppBar(
        title: 'Home',
        onNotificationTap: () {
          // Handle notification tap
        },
      ),
      body: Column(
        children: [
          // Search bar - clickable to navigate to search screen
          Padding(
            padding: const EdgeInsets.all(16),
            child: GestureDetector(
              onTap: () {
                context.pushNamed('search');
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                decoration: BoxDecoration(
                  color: AppTheme.surfaceColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppTheme.borderColor),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.search,
                      color: AppTheme.textSecondaryColor,
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Search For Exam',
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // Dynamic promotional banner
          _buildDynamicBanner(ref),

          // Main content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),

                  // Trending Exams section
                  _buildTrendingExams(context, ref),

                  const SizedBox(height: 24),

                  // Dynamic Live Test section
                  _buildDynamicLiveTests(context, ref),

                  const SizedBox(height: 24),

                  // Featured Quizzes
                  _buildFeaturedQuizzes(context, ref),

                  // Add some bottom padding to prevent overflow
                  const SizedBox(height: 20),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDynamicBanner(WidgetRef ref) {
    print('🔥 HomeScreen: Building dynamic banner...');
    final bannersAsync = ref.watch(bannersProvider);

    return bannersAsync.when(
      data: (banners) {
        print('✅ HomeScreen: Received ${banners.length} banners');
        if (banners.isEmpty) {
          print('⚠️ HomeScreen: No banners found, showing default');
          return const PromotionalBanner(
            title: 'ADMIN SYSTEM READY!',
            subtitle: 'Create banners in web admin to see them here',
            couponCode: 'ADMIN',
            discount: 'LIVE',
          );
        }

        // Filter active banners in memory
        final now = DateTime.now();
        final activeBanners = banners.where((banner) {
          final isInDateRange =
              banner.startDate.isBefore(now) && banner.endDate.isAfter(now);
          return banner.isActive && isInDateRange;
        }).toList();

        if (activeBanners.isEmpty) {
          print('⚠️ HomeScreen: No active banners found, showing default');
          return const PromotionalBanner(
            title: 'NO ACTIVE BANNERS',
            subtitle: 'Create active banners in web admin',
            couponCode: 'ADMIN',
            discount: 'READY',
          );
        }

        // Start auto-changing timer if multiple banners
        _startBannerTimer(activeBanners.length);

        // Show current banner based on index
        final bannerIndex = _currentBannerIndex % activeBanners.length;
        final banner = activeBanners[bannerIndex];
        print(
            '✅ HomeScreen: Showing banner ${bannerIndex + 1}/${activeBanners.length}: ${banner.title}');

        return AnimatedSwitcher(
          duration: const Duration(milliseconds: 500),
          child: PromotionalBanner(
            key: ValueKey(banner.id),
            title: banner.title,
            subtitle: banner.subtitle,
            couponCode: banner.couponCode,
            discount: banner.discount,
            primaryColor: _parseColor(banner.primaryColor),
            secondaryColor: _parseColor(banner.secondaryColor),
            icon: _parseIcon(banner.iconName),
            onTap: banner.examId != null
                ? () => _handleBannerTap(banner)
                : banner.targetUrl.isNotEmpty
                    ? () => _handleBannerTap(banner)
                    : null,
          ),
        );
      },
      loading: () {
        print('⏳ HomeScreen: Banner provider loading...');
        return const PromotionalBanner(
          title: 'LOADING...',
          subtitle: 'Please wait',
          couponCode: '',
          discount: '',
        );
      },
      error: (error, stackTrace) {
        print('❌ HomeScreen: Banner provider error: $error');
        return const PromotionalBanner(
          title: 'ERROR LOADING BANNERS',
          subtitle: 'Check Firebase connection',
          couponCode: 'ERROR',
          discount: '',
        );
      },
    );
  }

  Widget _buildDynamicLiveTests(BuildContext context, WidgetRef ref) {
    print('🔥 HomeScreen: Building dynamic live tests...');
    final liveTestsAsync = ref.watch(liveTestsProvider);

    return liveTestsAsync.when(
      data: (liveTests) {
        print('✅ HomeScreen: Received ${liveTests.length} live tests');
        if (liveTests.isEmpty) {
          print('⚠️ HomeScreen: No live tests found');
          return _buildNoLiveTests();
        }

        // Filter upcoming and live tests in memory
        final now = DateTime.now();
        final upcomingTests = liveTests
            .where((test) {
              final isUpcoming =
                  test.status == 'upcoming' && test.startTime.isAfter(now);
              final isLive = test.status == 'live' &&
                  test.startTime.isBefore(now) &&
                  test.endTime.isAfter(now);
              print(
                  '🔥 HomeScreen: Test ${test.title} - status: ${test.status}, isUpcoming: $isUpcoming, isLive: $isLive');
              return test.isActive && (isUpcoming || isLive);
            })
            .take(5)
            .toList();

        if (upcomingTests.isEmpty) {
          print('⚠️ HomeScreen: No upcoming live tests found');
          return _buildNoLiveTests();
        }

        // Show the next upcoming test
        final nextTest = upcomingTests.first;
        print('✅ HomeScreen: Showing live test: ${nextTest.title}');
        return _buildLiveTestCard(nextTest);
      },
      loading: () {
        print('⏳ HomeScreen: Live test provider loading...');
        return _buildLoadingLiveTest();
      },
      error: (error, stackTrace) {
        print('❌ HomeScreen: Live test provider error: $error');
        return _buildNoLiveTests();
      },
    );
  }

  Widget _buildLiveTestCard(LiveTestModel liveTest) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Live Test',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: liveTest.isCurrentlyLive
                          ? Colors.red.withOpacity(0.1)
                          : AppTheme.primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      liveTest.isCurrentlyLive ? Icons.live_tv : Icons.schedule,
                      color: liveTest.isCurrentlyLive
                          ? Colors.red
                          : AppTheme.primaryColor,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          liveTest.title,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: AppTheme.textPrimaryColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          liveTest.isCurrentlyLive
                              ? 'LIVE NOW - ${liveTest.currentParticipants} participants'
                              : 'Starts ${_formatDateTime(liveTest.startTime)}',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: liveTest.isCurrentlyLive
                                ? Colors.red
                                : AppTheme.textSecondaryColor,
                            fontWeight: liveTest.isCurrentlyLive
                                ? FontWeight.w600
                                : FontWeight.normal,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (liveTest.isCurrentlyLive)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'LIVE',
                        style: GoogleFonts.poppins(
                          fontSize: 10,
                          fontWeight: FontWeight.w700,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
              if (liveTest.description.isNotEmpty) ...[
                const SizedBox(height: 12),
                Text(
                  liveTest.description,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  // Helper methods for dynamic content
  Color _parseColor(String colorString) {
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return const Color(0xFFE91E63); // Default pink color
    }
  }

  IconData _parseIcon(String iconName) {
    switch (iconName.toLowerCase()) {
      case 'lightbulb':
        return Icons.lightbulb;
      case 'star':
        return Icons.star;
      case 'gift':
        return Icons.card_giftcard;
      case 'discount':
        return Icons.local_offer;
      case 'sale':
        return Icons.sell;
      case 'percent':
        return Icons.percent;
      default:
        return Icons.lightbulb;
    }
  }

  void _handleBannerTap(BannerModel banner) {
    print('Banner tapped: ${banner.title}');

    // If banner has an exam linked, navigate to that exam
    if (banner.examId != null && banner.examId!.isNotEmpty) {
      print('Navigating to exam: ${banner.examId} (${banner.examName})');
      context.goToQuiz(banner.examId!);
      return;
    }

    // If banner has a target URL, handle URL navigation
    if (banner.targetUrl.isNotEmpty) {
      print('Banner has target URL: ${banner.targetUrl}');
      // TODO: Implement URL handling if needed
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = dateTime.difference(now);

    if (difference.inDays > 0) {
      return 'in ${difference.inDays} day${difference.inDays > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      return 'in ${difference.inHours} hour${difference.inHours > 1 ? 's' : ''}';
    } else if (difference.inMinutes > 0) {
      return 'in ${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''}';
    } else {
      return 'starting soon';
    }
  }

  Widget _buildNoLiveTests() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Live Test',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey.shade50,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Row(
            children: [
              Icon(
                Icons.schedule,
                color: Colors.grey.shade400,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'No live tests scheduled at the moment',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingLiveTest() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Live Test',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: const Row(
            children: [
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              SizedBox(width: 12),
              Text('Loading live tests...'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTrendingExams(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Trending Exams',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () => context.goNamed('quiz-list'),
              child: Text(
                'View All',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Trending exams list
        SizedBox(
          height: 120,
          child: Consumer(
            builder: (context, ref, child) {
              final trendingExamsAsync = ref.watch(trendingExamsProvider);

              return trendingExamsAsync.when(
                data: (trendingExams) {
                  if (trendingExams.isEmpty) {
                    return Center(
                      child: Text(
                        'No trending exams available',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: AppTheme.textSecondaryColor,
                        ),
                      ),
                    );
                  }

                  return ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: trendingExams.length,
                    itemBuilder: (context, index) {
                      final exam = trendingExams[index];
                      return _buildTrendingExamCard(exam, index);
                    },
                  );
                },
                loading: () => const Center(
                  child: CircularProgressIndicator(),
                ),
                error: (error, stack) => Center(
                  child: Text(
                    'Error loading trending exams',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTrendingExamCard(ExamModel exam, int index) {
    // Get exam type color and icon
    Color color;
    String iconEmoji;

    switch (exam.examType) {
      case 'Postal guide':
        color = const Color(0xFF6366F1);
        iconEmoji = '📮';
        break;
      case 'Postal Volumes':
        color = const Color(0xFF10B981);
        iconEmoji = '📚';
        break;
      default:
        color = const Color(0xFF8B5CF6);
        iconEmoji = '📝';
    }

    return GestureDetector(
      onTap: () async {
        // Increment attempt count when user taps on trending exam
        final trendingService = ref.read(trendingExamServiceProvider);
        await trendingService.incrementExamAttempts(exam.id);

        // Navigate to quiz
        context.goToQuiz(exam.id);
      },
      child: Container(
        width: 100,
        margin: EdgeInsets.only(
          left: index == 0 ? 0 : 12,
          right: 12,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade200),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Trending badge
            if (exam.isTrending)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                margin: const EdgeInsets.only(bottom: 4),
                decoration: BoxDecoration(
                  color: Colors.orange,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '🔥 TRENDING',
                  style: GoogleFonts.poppins(
                    fontSize: 8,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),

            // Exam icon
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                iconEmoji,
                style: const TextStyle(fontSize: 20),
              ),
            ),
            const SizedBox(height: 6),

            // Exam name
            Text(
              exam.displayName,
              style: GoogleFonts.poppins(
                fontSize: 10,
                fontWeight: FontWeight.w500,
                color: AppTheme.textPrimaryColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            // Attempt count
            if (exam.totalAttempts > 0)
              Text(
                '${exam.totalAttempts} attempts',
                style: GoogleFonts.poppins(
                  fontSize: 8,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLiveTests(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Live Test',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade200),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.live_tv,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'IBPS RRB Officer Prelims Live Test 10',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Test Available From Jun 25 5:30 PM',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textSecondaryColor,
                size: 16,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuickStats() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Your Progress',
            style: GoogleFonts.poppins(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'Quizzes Taken',
                  '12',
                  Icons.quiz,
                  AppTheme.primaryColor,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Average Score',
                  '85%',
                  Icons.trending_up,
                  Colors.green,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'Streak',
                  '7 days',
                  Icons.local_fire_department,
                  Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: color,
            size: 24,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w700,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            color: AppTheme.textSecondaryColor,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildActionCard(
                'Take Quiz',
                'Start a new quiz',
                Icons.play_arrow,
                AppTheme.primaryColor,
                () => context.goNamed('quiz-list'),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildActionCard(
                'View Results',
                'Check your scores',
                Icons.assessment,
                Colors.blue,
                () => context.goNamed('profile'),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppTheme.borderColor),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 20,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: GoogleFonts.poppins(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            Text(
              subtitle,
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: GoogleFonts.poppins(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.borderColor),
          ),
          child: Column(
            children: [
              _buildActivityItem(
                'Mathematics Quiz',
                'Scored 90% • 2 hours ago',
                Icons.calculate,
                Colors.green,
              ),
              const Divider(),
              _buildActivityItem(
                'Science Quiz',
                'Scored 75% • Yesterday',
                Icons.science,
                Colors.blue,
              ),
              const Divider(),
              _buildActivityItem(
                'History Quiz',
                'Scored 85% • 2 days ago',
                Icons.history_edu,
                Colors.orange,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem(
    String title,
    String subtitle,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                ),
                Text(
                  subtitle,
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturedQuizzes(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Quizzes',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            TextButton(
              onPressed: () => context.goNamed('quiz-list'),
              child: Text(
                'View All',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: AppTheme.primaryColor,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),

        // Analytics Dashboard
        const AnalyticsDashboardWidget(),

        const SizedBox(height: 16),

        // User's Recent Quiz Attempts
        Consumer(
          builder: (context, ref, child) {
            final recentAttemptsAsync =
                ref.watch(userRecentAttemptsProvider(5));

            return recentAttemptsAsync.when(
              data: (attempts) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (attempts.isEmpty) ...[
                      // Hide empty state due to tight height constraints
                      // Just show a simple text message
                      Padding(
                        padding: const EdgeInsets.all(16),
                        child: Text(
                          'No recent quiz attempts',
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: AppTheme.textSecondaryColor,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ] else ...[
                      SizedBox(
                        height: 200, // Increased height to accommodate content
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 4), // Add padding
                          itemCount: attempts.length,
                          itemBuilder: (context, index) {
                            final attempt = attempts[index];
                            return _buildRecentAttemptCard(
                              attempt,
                              () => context.goToQuiz(attempt.examId),
                            );
                          },
                        ),
                      ),
                    ],
                  ],
                );
              },
              loading: () => const SizedBox(
                height: 160,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 8),
                      Text('Loading exams from Firebase...'),
                    ],
                  ),
                ),
              ),
              error: (error, stack) => Container(
                height: 200,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: AppTheme.surfaceColor,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppTheme.errorColor),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.error_outline,
                        size: 48,
                        color: AppTheme.errorColor,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Failed to load quizzes',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: AppTheme.errorColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Error: ${error.toString()}',
                        style: GoogleFonts.poppins(
                          fontSize: 10,
                          color: AppTheme.errorColor,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton(
                        onPressed: () =>
                            ref.refresh(featuredExamsStreamProvider),
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),

        // Add some bottom padding to prevent overflow
        const SizedBox(height: 20),
      ],
    );
  }

  Widget _buildExamCard(ExamModel exam, VoidCallback onTap) {
    // Get color based on exam type
    Color getExamColor(String examType) {
      switch (examType) {
        case 'Postal guide':
          return Colors.blue;
        case 'Postal Volumes':
          return Colors.green;
        default:
          return Colors.purple;
      }
    }

    final color = getExamColor(exam.examType);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 160,
        height: 180, // Add fixed height to prevent overflow
        margin: const EdgeInsets.only(right: 12),
        padding: const EdgeInsets.all(12), // Reduce padding slightly
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppTheme.borderColor),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min, // Use minimum space needed
          children: [
            // Exam type icon and status (consistent with web admin)
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    exam.typeIcon, // This uses the correct emoji from ExamModel
                    style: const TextStyle(
                        fontSize: 18), // Slightly smaller for better fit
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: exam.isReady ? Colors.green : Colors.orange,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    exam.isReady ? 'Ready' : 'Draft',
                    style: GoogleFonts.poppins(
                      fontSize: 10,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8), // Reduce spacing

            // Exam name - properly aligned
            Text(
              exam.displayName,
              style: GoogleFonts.poppins(
                fontSize: 13, // Slightly smaller font
                fontWeight: FontWeight.w600,
                color: AppTheme.textPrimaryColor,
                height: 1.2, // Better line height for alignment
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.left, // Ensure left alignment
            ),
            const SizedBox(height: 6), // Reduce spacing

            // Exam details
            Row(
              children: [
                const Icon(
                  Icons.quiz,
                  size: 12, // Smaller icon
                  color: AppTheme.textSecondaryColor,
                ),
                const SizedBox(width: 3),
                Flexible(
                  child: Text(
                    '${exam.questions.length} Questions',
                    style: GoogleFonts.poppins(
                      fontSize: 11, // Smaller font
                      color: AppTheme.textSecondaryColor,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 3), // Reduce spacing
            Row(
              children: [
                const Icon(
                  Icons.access_time,
                  size: 12, // Smaller icon
                  color: AppTheme.textSecondaryColor,
                ),
                const SizedBox(width: 3),
                Flexible(
                  child: Text(
                    exam.formattedDuration,
                    style: GoogleFonts.poppins(
                      fontSize: 11, // Smaller font
                      color: AppTheme.textSecondaryColor,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 6), // Reduce spacing

            // Suitable for tags - use remaining space efficiently
            Expanded(
              child: Align(
                alignment: Alignment.bottomLeft,
                child: Wrap(
                  spacing: 3,
                  runSpacing: 2,
                  children: exam.suitableFor.take(2).map((role) {
                    return Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 4, vertical: 1),
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(3),
                      ),
                      child: Text(
                        role,
                        style: GoogleFonts.poppins(
                          fontSize: 9, // Smaller font
                          fontWeight: FontWeight.w500,
                          color: color,
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentAttemptCard(QuizAttemptModel attempt, VoidCallback onTap) {
    // Get color based on exam type
    Color getExamColor(String examType) {
      switch (examType) {
        case 'Postal guide':
          return const Color(0xFF6366F1);
        case 'Postal Volumes':
          return const Color(0xFF10B981);
        default:
          return const Color(0xFF8B5CF6);
      }
    }

    // Get status color
    Color getStatusColor(String status) {
      switch (status) {
        case 'completed':
          return Colors.green;
        case 'in_progress':
          return Colors.orange;
        case 'abandoned':
          return Colors.red;
        default:
          return Colors.grey;
      }
    }

    // Get exam icon
    String getExamIcon(String examType) {
      switch (examType) {
        case 'Postal guide':
          return '📮';
        case 'Postal Volumes':
          return '📚';
        default:
          return '📝';
      }
    }

    final color = getExamColor(attempt.examType);
    final statusColor = getStatusColor(attempt.status);
    final icon = getExamIcon(attempt.examType);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 160,
        margin: const EdgeInsets.only(right: 12),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: AppTheme.borderColor),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(10), // Reduced padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // Use minimum space needed
            children: [
              // Header with icon and status
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.all(6), // Reduced padding
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      icon,
                      style: const TextStyle(fontSize: 18), // Slightly smaller
                    ),
                  ),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      attempt.statusDisplayText,
                      style: GoogleFonts.poppins(
                        fontSize: 8,
                        fontWeight: FontWeight.w600,
                        color: statusColor,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 6), // Reduced spacing

              // Exam name
              Flexible(
                child: Text(
                  attempt.displayName,
                  style: GoogleFonts.poppins(
                    fontSize: 13, // Slightly smaller
                    fontWeight: FontWeight.w600,
                    color: AppTheme.textPrimaryColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(height: 3), // Reduced spacing

              // Exam type
              Text(
                attempt.examType,
                style: GoogleFonts.poppins(
                  fontSize: 9, // Slightly smaller
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 6), // Reduced spacing

              // Score and time info
              if (attempt.isCompleted && attempt.score != null) ...[
                Row(
                  children: [
                    const Icon(
                      Icons.star,
                      size: 11, // Slightly smaller
                      color: Colors.amber,
                    ),
                    const SizedBox(width: 3), // Reduced spacing
                    Text(
                      '${attempt.score}/${attempt.totalQuestions}',
                      style: GoogleFonts.poppins(
                        fontSize: 9, // Slightly smaller
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                    if (attempt.scorePercentage != null) ...[
                      const SizedBox(width: 6), // Reduced spacing
                      Text(
                        '${attempt.scorePercentage!.toStringAsFixed(0)}%',
                        style: GoogleFonts.poppins(
                          fontSize: 9, // Slightly smaller
                          fontWeight: FontWeight.w600,
                          color: attempt.scorePercentage! >= 60
                              ? Colors.green
                              : Colors.red,
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 3), // Reduced spacing
              ],

              // Attempt time
              Row(
                children: [
                  const Icon(
                    Icons.access_time,
                    size: 11, // Slightly smaller
                    color: AppTheme.textSecondaryColor,
                  ),
                  const SizedBox(width: 3), // Reduced spacing
                  Flexible(
                    child: Text(
                      attempt.formattedAttemptDate,
                      style: GoogleFonts.poppins(
                        fontSize: 9, // Slightly smaller
                        color: AppTheme.textSecondaryColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
