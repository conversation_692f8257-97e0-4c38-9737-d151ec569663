import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../services/firebase_auth_service.dart';

// Auth state class with error handling
class AuthState {
  final bool isLoading;
  final bool isAuthenticated;
  final String? error;
  final User? user;

  const AuthState({
    this.isLoading = false,
    this.isAuthenticated = false,
    this.error,
    this.user,
  });

  static const initial = AuthState();
  static const loading = AuthState(isLoading: true);
  static const unauthenticated = AuthState(isAuthenticated: false);

  AuthState copyWith({
    bool? isLoading,
    bool? isAuthenticated,
    String? error,
    User? user,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      error: error,
      user: user ?? this.user,
    );
  }
}

// Auth state notifier
class AuthNotifier extends StateNotifier<AuthState> {
  // final FirebaseAuthService _authService; // Unused for now
  String? _verificationId;

  AuthNotifier(FirebaseAuthService authService) : super(AuthState.initial) {
    _init();
  }

  User? get currentUser => state.user;

  void _init() {
    FirebaseAuth.instance.authStateChanges().listen((user) {
      if (user != null) {
        state = AuthState(isAuthenticated: true, user: user);
      } else {
        state = AuthState.unauthenticated;
      }
    });
  }

  Future<bool> sendOTP(String phoneNumber) async {
    state = AuthState.loading;
    try {
      await FirebaseAuth.instance.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          await FirebaseAuth.instance.signInWithCredential(credential);
        },
        verificationFailed: (FirebaseAuthException e) {
          state = state.copyWith(isLoading: false, error: e.message);
        },
        codeSent: (String verificationId, int? resendToken) {
          _verificationId = verificationId;
          state = state.copyWith(isLoading: false);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          _verificationId = verificationId;
        },
      );
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
    return true;
  }

  Future<bool> verifyOTP(String otp) async {
    if (_verificationId == null) {
      state = state.copyWith(error: 'Verification ID not found');
      return false;
    }

    state = AuthState.loading;
    try {
      final credential = PhoneAuthProvider.credential(
        verificationId: _verificationId!,
        smsCode: otp,
      );
      await FirebaseAuth.instance.signInWithCredential(credential);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return false;
    }
  }

  Future<void> signOut() async {
    state = AuthState.loading;
    try {
      await FirebaseAuth.instance.signOut();
      state = AuthState.unauthenticated;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<bool> registerWithPhone(
      String phoneNumber, Map<String, dynamic> userData) async {
    return await sendOTP(phoneNumber);
  }

  Future<bool> verifyRegistrationOTP(
      String otp, Map<String, dynamic> userData) async {
    final success = await verifyOTP(otp);
    if (success) {
      // Save user data to Firestore after successful verification
      // TODO: Implement user data saving
    }
    return success;
  }

  Future<bool> resendRegistrationOTP(String phoneNumber) async {
    return await sendOTP(phoneNumber);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Providers
final firebaseAuthServiceProvider = Provider<FirebaseAuthService>((ref) {
  return FirebaseAuthService();
});

final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final authService = ref.watch(firebaseAuthServiceProvider);
  return AuthNotifier(authService);
});

final currentUserProvider = Provider<User?>((ref) {
  final authNotifier = ref.watch(authProvider.notifier);
  return authNotifier.currentUser;
});
