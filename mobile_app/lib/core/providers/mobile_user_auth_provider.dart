import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
// Removed: import '../services/mobile_user_auth_service.dart';
import '../services/supabase_auth_service.dart';

/// Mobile User Model (separate from admin users)
class MobileUser {
  final String uid;
  final String email;
  final String name;
  final String phoneNumber;
  final String officeName;
  final String designation;
  final bool emailVerified;
  final bool isActive;
  final int quizzesTaken;
  final int totalScore;
  final double averageScore;
  final DateTime? createdAt;
  final DateTime? lastLoginAt;
  final Map<String, dynamic>? preferences;

  const MobileUser({
    required this.uid,
    required this.email,
    required this.name,
    required this.phoneNumber,
    required this.officeName,
    required this.designation,
    required this.emailVerified,
    required this.isActive,
    this.quizzesTaken = 0,
    this.totalScore = 0,
    this.averageScore = 0.0,
    this.createdAt,
    this.lastLoginAt,
    this.preferences,
  });

  factory MobileUser.fromFirestore(Map<String, dynamic> data) {
    return MobileUser(
      uid: data['uid'] ?? '',
      email: data['email'] ?? '',
      name: data['name'] ?? '',
      phoneNumber: data['phoneNumber'] ?? '',
      officeName: data['officeName'] ?? '',
      designation: data['designation'] ?? '',
      emailVerified: data['emailVerified'] ?? false,
      isActive: data['isActive'] ?? true,
      quizzesTaken: data['quizzesTaken'] ?? 0,
      totalScore: data['totalScore'] ?? 0,
      averageScore: (data['averageScore'] ?? 0.0).toDouble(),
      createdAt: data['createdAt']?.toDate(),
      lastLoginAt: data['lastLoginAt']?.toDate(),
      preferences: data['preferences'],
    );
  }

  MobileUser copyWith({
    String? uid,
    String? email,
    String? name,
    String? phoneNumber,
    String? officeName,
    String? designation,
    bool? emailVerified,
    bool? isActive,
    int? quizzesTaken,
    int? totalScore,
    double? averageScore,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    Map<String, dynamic>? preferences,
  }) {
    return MobileUser(
      uid: uid ?? this.uid,
      email: email ?? this.email,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      officeName: officeName ?? this.officeName,
      designation: designation ?? this.designation,
      emailVerified: emailVerified ?? this.emailVerified,
      isActive: isActive ?? this.isActive,
      quizzesTaken: quizzesTaken ?? this.quizzesTaken,
      totalScore: totalScore ?? this.totalScore,
      averageScore: averageScore ?? this.averageScore,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
    );
  }
}

/// Mobile User Authentication State
class MobileUserAuthState {
  final MobileUser? user;
  final bool isLoading;
  final bool isRegistering;
  final bool isLoggingIn;
  final String? error;
  final bool isAuthenticated;

  const MobileUserAuthState({
    this.user,
    this.isLoading = false,
    this.isRegistering = false,
    this.isLoggingIn = false,
    this.error,
    this.isAuthenticated = false,
  });

  MobileUserAuthState copyWith({
    MobileUser? user,
    bool? isLoading,
    bool? isRegistering,
    bool? isLoggingIn,
    String? error,
    bool? isAuthenticated,
  }) {
    return MobileUserAuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      isRegistering: isRegistering ?? this.isRegistering,
      isLoggingIn: isLoggingIn ?? this.isLoggingIn,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

/// Mobile User Authentication Provider
class MobileUserAuthNotifier extends StateNotifier<MobileUserAuthState> {
  bool _isCreatingMissingDocument = false;

  MobileUserAuthNotifier() : super(const MobileUserAuthState()) {
    _checkAuthState();
    _listenToAuthChanges();
  }

  /// Check current authentication state
  void _checkAuthState() async {
    final user = SupabaseAuthService.getCurrentUser();
    if (user != null) {
      await _loadUserData(user.id);
    }
  }

  /// Listen to Firebase Auth state changes
  void _listenToAuthChanges() {
    Supabase.instance.client.auth.onAuthStateChange.listen((data) async {
      final User? user = data.session?.user;
      if (user != null) {
        await _loadUserData(user.id);
      } else {
        state = const MobileUserAuthState();
      }
    });
  }

  /// Load user data from Firestore
  Future<void> _loadUserData(String uid) async {
    try {
      if (kDebugMode) {
        print('DEBUG: 🔄 _loadUserData called for UID: $uid');
      }
      final userData = await SupabaseAuthService.getUserData(uid);
      if (kDebugMode) {
        print(
            'DEBUG: 📊 userData result: ${userData != null ? "NOT NULL" : "NULL"}');
      }
      if (userData != null) {
        final mobileUser = MobileUser.fromFirestore(userData);
        state = state.copyWith(
          user: mobileUser,
          isAuthenticated: true,
          error: null,
        );
      } else {
        // User document not found in Firestore but user exists in Supabase
        // This can happen if registration was incomplete
        if (kDebugMode) {
          print(
              'DEBUG: ⚠️ User document missing, attempting to create from Supabase user data');
          print(
              'DEBUG: 🔍 Current _isCreatingMissingDocument flag: $_isCreatingMissingDocument');
        }

        // Prevent multiple simultaneous attempts to create missing document
        if (_isCreatingMissingDocument) {
          if (kDebugMode) {
            print('DEBUG: ⏳ Already creating missing document, skipping...');
          }
          return;
        }

        final supabaseUser = SupabaseAuthService.getCurrentUser();
        if (supabaseUser != null) {
          _isCreatingMissingDocument = true;
          try {
            if (kDebugMode) {
              print('DEBUG: 🔧 Attempting to create missing user document...');
            }
            await _createMissingUserDocument(supabaseUser);

            if (kDebugMode) {
              print(
                  'DEBUG: 🔄 Retrying to load user data after document creation...');
            }
            // Retry loading user data after creating the document
            final retryUserData = await SupabaseAuthService.getUserData(uid);
            if (retryUserData != null) {
              if (kDebugMode) {
                print(
                    'DEBUG: ✅ User data loaded successfully after document creation');
              }
              final mobileUser = MobileUser.fromFirestore(retryUserData);
              state = state.copyWith(
                user: mobileUser,
                isAuthenticated: true,
                error: null,
              );
            } else {
              // If still no user data, sign out the user
              if (kDebugMode) {
                print(
                    'DEBUG: ❌ Unable to create or load user data, signing out');
              }
              await signOut();
            }
          } finally {
            _isCreatingMissingDocument = false;
          }
        } else {
          // No Supabase user, sign out
          if (kDebugMode) {
            print('DEBUG: ❌ No Supabase user found, signing out');
          }
          await signOut();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to load user data: $e');
      }
      state = state.copyWith(error: e.toString());
    }
  }

  /// Create missing user document from Supabase user data
  Future<void> _createMissingUserDocument(User supabaseUser) async {
    try {
      if (kDebugMode) {
        print(
            'DEBUG: 🔧 Creating missing user document for: ${supabaseUser.id}');
      }

      // Extract user data from Supabase user metadata
      final userMetadata = supabaseUser.userMetadata ?? {};
      final name = userMetadata['name'] as String? ??
          supabaseUser.email?.split('@').first ??
          'User';
      final phoneNumber = userMetadata['phone_number'] as String? ?? '';
      final officeName = userMetadata['office_name'] as String? ?? '';
      final designation = userMetadata['designation'] as String? ?? '';

      // Create user document in Firestore using the Supabase auth service
      await SupabaseAuthService.createUserDocumentFromSupabaseUser(
        uid: supabaseUser.id,
        email: supabaseUser.email ?? '',
        name: name,
        phoneNumber: phoneNumber,
        officeName: officeName,
        designation: designation,
      );

      if (kDebugMode) {
        print('DEBUG: ✅ Missing user document created successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to create missing user document: $e');
      }
      rethrow;
    }
  }

  /// Register new mobile user
  Future<bool> registerUser({
    required String email,
    required String password,
    required String name,
    required String phoneNumber,
    required String officeName,
    required String designation,
  }) async {
    state = state.copyWith(isRegistering: true, error: null);

    try {
      // Use Supabase authentication service
      final result = await SupabaseAuthService.registerUser(
        email: email,
        password: password,
        name: name,
        phoneNumber: phoneNumber,
        officeName: officeName,
        designation: designation,
      );

      if (result['success'] == true) {
        final uid = result['uid'] as String;
        await _loadUserData(uid);
        state = state.copyWith(isRegistering: false);
        return true;
      } else {
        state = state.copyWith(
          isRegistering: false,
          error: result['message'] as String? ?? 'Registration failed',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isRegistering: false,
        error: _getErrorMessage(e),
      );
      return false;
    }
  }

  /// Sign in mobile user
  Future<bool> signInUser({
    required String email,
    required String password,
  }) async {
    state = state.copyWith(isLoggingIn: true, error: null);

    try {
      // Use Supabase authentication service
      final result = await SupabaseAuthService.signInUser(
        email: email,
        password: password,
      );

      if (result['success'] == true) {
        final uid = result['uid'] as String;
        await _loadUserData(uid);
        state = state.copyWith(isLoggingIn: false);
        return true;
      } else {
        state = state.copyWith(
          isLoggingIn: false,
          error: result['message'] as String? ?? 'Sign in failed',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoggingIn: false,
        error: _getErrorMessage(e),
      );
      return false;
    }
  }

  /// Send password reset email
  Future<bool> sendPasswordReset(String email) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await SupabaseAuthService.sendPasswordReset(email);

      if (result['success'] == true) {
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result['message'] as String? ?? 'Password reset failed',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: _getErrorMessage(e),
      );
      return false;
    }
  }

  /// Sign out user
  Future<void> signOut() async {
    try {
      await SupabaseAuthService.signOut();
      state = const MobileUserAuthState();
    } catch (e) {
      state = state.copyWith(error: _getErrorMessage(e));
    }
  }

  /// Update user profile
  Future<bool> updateProfile({
    String? name,
    String? phoneNumber,
    String? officeName,
    String? designation,
    Map<String, dynamic>? preferences,
  }) async {
    if (state.user == null) return false;

    state = state.copyWith(isLoading: true, error: null);

    try {
      // TODO: Implement profile update in Supabase service
      // For now, just reload user data

      await _loadUserData(state.user!.uid);
      state = state.copyWith(isLoading: false);
      return true;
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: _getErrorMessage(e),
      );
      return false;
    }
  }

  /// Update quiz statistics
  Future<void> updateQuizStats({
    required int score,
    required int totalQuestions,
  }) async {
    if (state.user == null) return;

    try {
      // TODO: Implement quiz stats update in Supabase service
      // For now, just reload user data
      await _loadUserData(state.user!.uid);
    } catch (e) {
      if (kDebugMode) {
        print('DEBUG: ❌ Failed to update quiz stats: $e');
      }
    }
  }

  /// Clear error state
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// Get user-friendly error message
  String _getErrorMessage(dynamic error) {
    final errorString = error.toString();

    // Handle PigeonUserDetails error specifically
    if (errorString.contains('PigeonUserDetails') ||
        errorString.contains('type cast')) {
      return 'Authentication error occurred. Please try again.';
    }

    if (error is AuthException) {
      switch (error.statusCode) {
        case '400':
          if (error.message.contains('email')) {
            return 'Please enter a valid email address.';
          } else if (error.message.contains('password')) {
            return 'Password must be at least 6 characters long.';
          }
          return 'Invalid request. Please check your input.';
        case '422':
          if (error.message.contains('already registered')) {
            return 'An account already exists with this email address.';
          }
          return 'Email already registered or invalid format.';
        case '401':
          return 'Invalid email or password.';
        case '429':
          return 'Too many requests. Please try again later.';
        case '500':
          return 'Server error. Please try again later.';
        default:
          return error.message;
      }
    }

    // Handle other common errors
    if (errorString.contains('network')) {
      return 'Network error. Please check your internet connection.';
    }

    if (errorString.contains('timeout')) {
      return 'Request timed out. Please try again.';
    }

    return 'An unexpected error occurred. Please try again.';
  }
}

/// Mobile User Authentication Provider
final mobileUserAuthProvider =
    StateNotifierProvider<MobileUserAuthNotifier, MobileUserAuthState>((ref) {
  return MobileUserAuthNotifier();
});

/// Current Mobile User Provider
final currentMobileUserProvider = Provider<MobileUser?>((ref) {
  final authState = ref.watch(mobileUserAuthProvider);
  return authState.user;
});
