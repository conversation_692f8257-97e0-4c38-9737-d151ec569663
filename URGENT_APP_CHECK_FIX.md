# URGENT: App Check Blocking SMS Authentication

## Error Analysis
```
Invalid app info in play_integrity_token
```
This confirms App Check is enabled and blocking your authentication requests.

## IMMEDIATE SOLUTION

### Step 1: Disable App Check Enforcement
1. **Firebase Console** → **App Check**
2. **Find your Android app**: `com.mcqquiz.app`
3. **Look for "Enforcement" toggle**
4. **Turn OFF enforcement** for:
   - Authentication
   - Firestore (if enabled)
5. **Save changes**

### Step 2: Verify Authentication Settings
1. **Authentication** → **Sign-in method**
2. **Ensure "Phone" is ENABLED** (green toggle)
3. **Add test phone numbers**:
   - Phone: `******-555-3434`, Code: `123456`
   - Phone: `+91 98765 43210`, Code: `654321`

### Step 3: Clean Rebuild
```bash
cd mobile_app
flutter clean
rm -rf android/.gradle
flutter pub get
flutter build apk --debug
```

## ALTERNATIVE SOLUTIONS

### Option A: Properly Configure App Check
If you want to keep App Check enabled:

1. **App Check** → **Register app**
2. **Select "Play Integrity API"**
3. **Add SHA-256**: `1B:99:E7:43:E5:B4:B7:AA:B4:2F:2B:E3:D2:D8:60:E8:E1:31:AE:E9:F7:20:4E:6C:D6:8E:36:4D:99:F4:84:E3`
4. **Wait 30 minutes** for propagation
5. **Test again**

### Option B: Use Debug Token (Development)
1. **App Check** → **Apps** → **Your app**
2. **Generate debug token**
3. **Add to your app code**:
```dart
if (kDebugMode) {
  await FirebaseAppCheck.instance.activate(
    androidProvider: AndroidProvider.debug,
  );
}
```

### Option C: Complete App Check Removal
1. **App Check** → **Apps**
2. **Delete/Unregister** your Android app
3. **Disable App Check** entirely

## RECOMMENDED APPROACH

**For Development**: Disable App Check enforcement
**For Production**: Properly configure with Play Integrity API

## VERIFICATION STEPS

After disabling App Check:
1. **Test with debug build**
2. **Try phone authentication**
3. **Check for error messages**
4. **Verify OTP delivery**

## FIREBASE CONSOLE CHECKLIST

- [ ] App Check enforcement DISABLED
- [ ] Phone authentication ENABLED
- [ ] Test phone numbers added
- [ ] SHA fingerprints correctly configured
- [ ] Package name matches: `com.mcqquiz.app`

## IMMEDIATE ACTION

**Priority 1**: Disable App Check enforcement
**Priority 2**: Test authentication immediately
**Priority 3**: Re-enable App Check later with proper configuration

The error will disappear once App Check stops blocking your requests.
