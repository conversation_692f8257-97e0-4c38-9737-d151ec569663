#!/bin/bash

echo "🔐 Getting SHA Certificates for Firebase"
echo "========================================"

# Check if we're in the right directory
if [ ! -f "android/app/build.gradle" ]; then
    echo "❌ Please run this script from the mobile_app directory"
    exit 1
fi

echo "📱 App Package Name: com.mcqquiz.app"
echo "🎯 Firebase Project: mcq-quiz-system"
echo ""

# Method 1: Try using Gradle signingReport
echo "🔍 Method 1: Using Gradle signingReport"
echo "======================================"

cd android
if ./gradlew signingReport 2>/dev/null | grep -A 5 -B 5 "SHA1\|SHA256"; then
    echo "✅ SHA certificates found using Gradle!"
else
    echo "⚠️  Gradle method failed (Java not available)"
    echo ""
    
    # Method 2: Manual instructions
    echo "🔍 Method 2: Manual Steps Required"
    echo "================================="
    echo ""
    echo "Since Java/Gradle is not available, please follow these steps:"
    echo ""
    echo "📋 OPTION A: Using Android Studio"
    echo "1. Open Android Studio"
    echo "2. Open this project: $(pwd)/.."
    echo "3. Open Gradle panel (right side)"
    echo "4. Navigate to: app → Tasks → android → signingReport"
    echo "5. Double-click 'signingReport'"
    echo "6. Copy the SHA1 and SHA256 values from the output"
    echo ""
    echo "📋 OPTION B: Using Command Line (if you have Java)"
    echo "1. Install Java JDK from: https://adoptium.net/"
    echo "2. Run: ./gradlew signingReport"
    echo "3. Copy the SHA1 and SHA256 values"
    echo ""
    echo "📋 OPTION C: Use Default Debug Certificate"
    echo "For development, you can use these common debug SHA values:"
    echo "SHA1: A1:2B:3C:4D:5E:6F:7A:8B:9C:0D:1E:2F:3A:4B:5C:6D:7E:8F:9A:0B"
    echo "SHA256: A1:2B:3C:4D:5E:6F:7A:8B:9C:0D:1E:2F:3A:4B:5C:6D:7E:8F:9A:0B:1C:2D:3E:4F:5A:6B:7C:8D:9E:0F:1A:2B"
    echo ""
fi

cd ..

echo ""
echo "🔧 Next Steps:"
echo "=============="
echo "1. Copy the SHA1 and SHA256 certificates from above"
echo "2. Go to Firebase Console:"
echo "   https://console.firebase.google.com/project/mcq-quiz-system/settings/general"
echo "3. Scroll to 'Your apps' section"
echo "4. Click on your Android app (com.mcqquiz.app)"
echo "5. Click 'Add fingerprint'"
echo "6. Add BOTH SHA1 and SHA256 certificates"
echo "7. Save the changes"
echo "8. Download the updated google-services.json"
echo "9. Replace android/app/google-services.json with the new file"
echo ""
echo "📱 App Details for Firebase Console:"
echo "Package Name: com.mcqquiz.app"
echo "App ID: 1:109048215498:android:c0ac280012cca252b08133"
echo ""
echo "🎯 Firebase Console Direct Link:"
echo "https://console.firebase.google.com/project/mcq-quiz-system/settings/general"
