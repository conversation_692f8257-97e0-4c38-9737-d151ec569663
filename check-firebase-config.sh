#!/bin/bash

echo "🔍 Firebase Configuration Checker"
echo "=================================="

# Check if google-services.json exists
if [ -f "mobile_app/android/app/google-services.json" ]; then
    echo "✅ google-services.json found"
    
    # Extract key information
    PROJECT_ID=$(grep -o '"project_id": "[^"]*"' mobile_app/android/app/google-services.json | cut -d'"' -f4)
    PACKAGE_NAME=$(grep -o '"package_name": "[^"]*"' mobile_app/android/app/google-services.json | cut -d'"' -f4)
    CERT_HASH=$(grep -o '"certificate_hash": "[^"]*"' mobile_app/android/app/google-services.json | cut -d'"' -f4)
    
    echo "📋 Project ID: $PROJECT_ID"
    echo "📦 Package Name: $PACKAGE_NAME"
    echo "🔑 Certificate Hash: $CERT_HASH"
else
    echo "❌ google-services.json not found!"
fi

echo ""
echo "🔧 Build Configuration Check"
echo "----------------------------"

# Check build.gradle
if [ -f "mobile_app/android/app/build.gradle" ]; then
    APP_ID=$(grep -o 'applicationId "[^"]*"' mobile_app/android/app/build.gradle | cut -d'"' -f2)
    echo "📱 Application ID: $APP_ID"
    
    # Check if google-services plugin is applied
    if grep -q "com.google.gms.google-services" mobile_app/android/app/build.gradle; then
        echo "✅ Google Services plugin applied"
    else
        echo "❌ Google Services plugin missing"
    fi
else
    echo "❌ build.gradle not found!"
fi

echo ""
echo "🔍 SHA Fingerprint Verification"
echo "------------------------------"

# Get current SHA fingerprint
if command -v keytool &> /dev/null; then
    CURRENT_SHA=$(keytool -list -v -alias androiddebugkey -keystore ~/.android/debug.keystore -storepass android -keypass android 2>/dev/null | grep "SHA1:" | cut -d' ' -f3)
    echo "🔑 Current SHA-1: $CURRENT_SHA"
    
    # Compare with google-services.json
    if [ -n "$CERT_HASH" ] && [ -n "$CURRENT_SHA" ]; then
        CURRENT_SHA_LOWER=$(echo "$CURRENT_SHA" | tr '[:upper:]' '[:lower:]' | tr -d ':')
        if [ "$CERT_HASH" = "$CURRENT_SHA_LOWER" ]; then
            echo "✅ SHA fingerprints match!"
        else
            echo "❌ SHA fingerprints don't match!"
            echo "   Expected: $CERT_HASH"
            echo "   Current:  $CURRENT_SHA_LOWER"
        fi
    fi
else
    echo "❌ keytool not found (Java not installed)"
fi

echo ""
echo "📋 Manual Verification Steps"
echo "----------------------------"
echo "1. Go to Firebase Console: https://console.firebase.google.com/"
echo "2. Select project: $PROJECT_ID"
echo "3. Go to Authentication → Sign-in method"
echo "4. Verify 'Phone' is ENABLED (not just added)"
echo "5. Check App Check is disabled (temporarily)"
echo "6. For production: Enable billing in Usage & billing"

echo ""
echo "🚀 Quick Fix Commands"
echo "--------------------"
echo "# Clean rebuild:"
echo "cd mobile_app && flutter clean && flutter build apk --debug"
echo ""
echo "# Test with debug build:"
echo "flutter run --debug"

echo ""
echo "📞 Test Phone Numbers to Add in Firebase Console:"
echo "------------------------------------------------"
echo "Phone: ******-555-3434, Code: 123456"
echo "Phone: +91 98765 43210, Code: 654321"
